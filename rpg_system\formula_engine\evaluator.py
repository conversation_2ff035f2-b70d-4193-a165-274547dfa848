"""
安全的公式求值引擎
用於計算JSON配置中的公式字符串
使用 asteval 庫進行安全的表達式求值
"""

import re
import math
from typing import Dict, Any, Union, Optional
from utils.logger import logger

try:
    from asteval import Interpreter
except ImportError:
    logger.error("asteval 庫未安裝，請運行: pip install asteval")
    raise


class FormulaEvaluator:
    """安全的公式求值器，使用 asteval 進行安全的表達式求值"""

    def __init__(self):
        """初始化公式求值器"""
        # 創建 asteval 解釋器
        self.interpreter = Interpreter()

        # 設置允許的函數
        self._setup_allowed_functions()

        # 編譯正則表達式
        self.number_pattern = re.compile(r'^\d+(\.\d+)?$')

    def _setup_allowed_functions(self):
        """設置允許的函數和常量"""
        # 清除所有內建函數（安全起見）
        self.interpreter.symtable.clear()

        # 添加數學函數
        self.interpreter.symtable['min'] = min
        self.interpreter.symtable['max'] = max
        self.interpreter.symtable['abs'] = abs
        self.interpreter.symtable['round'] = round
        self.interpreter.symtable['floor'] = math.floor
        self.interpreter.symtable['ceil'] = math.ceil
        self.interpreter.symtable['sqrt'] = math.sqrt
        self.interpreter.symtable['pow'] = pow
        self.interpreter.symtable['ln'] = math.log
        self.interpreter.symtable['log10'] = math.log10

        # 添加邏輯函數
        def iff(condition, value_if_true, value_if_false):
            """條件函數，避免與 Python 的 if 關鍵字衝突"""
            return value_if_true if condition else value_if_false

        def clamp(value, min_val, max_val):
            """限制值在指定範圍內"""
            return max(min_val, min(max_val, value))

        def log_func(x, base=math.e):
            """對數函數"""
            return math.log(x, base)

        def if_func(condition, value_if_true, value_if_false):
            """另一個條件函數，使用 if 名稱"""
            return value_if_true if condition else value_if_false

        self.interpreter.symtable['iff'] = iff  # 使用 iff 而不是 if
        self.interpreter.symtable['if'] = if_func  # 也支持 if 函數
        self.interpreter.symtable['clamp'] = clamp
        self.interpreter.symtable['log'] = log_func

        # 添加常量
        self.interpreter.symtable['PI'] = math.pi
        self.interpreter.symtable['E'] = math.e
    
    def evaluate(self, formula: str, context: Dict[str, Any]) -> float:
        """
        求值公式

        Args:
            formula: 公式字符串
            context: 變量上下文

        Returns:
            計算結果
        """
        if not formula or not isinstance(formula, str):
            return 0.0

        try:
            # 清理公式字符串
            formula = formula.strip()

            # 如果是純數字，直接返回
            if self.number_pattern.match(formula):
                return float(formula)

            # 設置上下文變量到解釋器
            self._set_context_variables(context)

            # 使用 asteval 求值
            result = self.interpreter.eval(formula)

            # 檢查是否有錯誤（asteval 使用不同的錯誤處理方式）
            if hasattr(self.interpreter, 'errors') and self.interpreter.errors:
                error_msg = '; '.join(self.interpreter.errors)
                logger.warning(f"公式求值警告: {formula}, 錯誤: {error_msg}")
                self.interpreter.errors.clear()
                return 0.0

            # 檢查結果是否為 None（通常表示錯誤）
            if result is None:
                logger.warning(f"公式求值返回 None: {formula}")
                return 0.0

            # 確保返回數值類型
            if isinstance(result, (int, float)):
                return float(result)
            else:
                logger.warning(f"公式返回非數值類型: {formula} -> {type(result)}")
                return 0.0

        except Exception as e:
            logger.error(f"公式求值錯誤: {formula}, 錯誤: {e}")
            return 0.0

    def _set_context_variables(self, context: Dict[str, Any]) -> None:
        """
        設置上下文變量到解釋器

        Args:
            context: 變量上下文
        """
        # 清除之前的變量（保留函數和常量）
        vars_to_remove = []
        for key in self.interpreter.symtable:
            if not callable(self.interpreter.symtable[key]) and key not in ['PI', 'E']:
                vars_to_remove.append(key)

        for key in vars_to_remove:
            del self.interpreter.symtable[key]

        # 設置新的上下文變量
        for key, value in context.items():
            # 處理點號分隔的變量名（asteval 不直接支持，需要扁平化）
            if '.' in key:
                # 將點號替換為下劃線
                safe_key = key.replace('.', '_')
                self.interpreter.symtable[safe_key] = value
            else:
                self.interpreter.symtable[key] = value
    



# 全局公式求值器實例
_formula_evaluator: Optional[FormulaEvaluator] = None


def get_formula_evaluator() -> FormulaEvaluator:
    """獲取全局公式求值器實例"""
    global _formula_evaluator
    if _formula_evaluator is None:
        _formula_evaluator = FormulaEvaluator()
    return _formula_evaluator


def evaluate_formula(formula: str, context: Dict[str, Any]) -> float:
    """
    求值公式的便捷函數
    
    Args:
        formula: 公式字符串
        context: 變量上下文
        
    Returns:
        計算結果
    """
    evaluator = get_formula_evaluator()
    return evaluator.evaluate(formula, context)
