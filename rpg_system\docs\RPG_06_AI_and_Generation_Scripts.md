**VI. AI與生成腳本職責**

*   **AI輔助設計：** 提供天賦/通用技能創意、描述、效果思路；平衡參數建議；文本生成。
*   **生成腳本：**
    *   核心任務：結構化AI提供的內容，生成所有JSON配置文件。
    *   為 `cards.json` 分配天賦ID、計算基礎屬性/成長、分配槽位等。
    *   迭代：修改規則/AI輸入，重新生成配置文件以調整平衡或新增內容。

### 2. 卡牌配置生成 (`cards.json`)

*   **目標**：為現有的 `gacha_master_cards` 表中的每一張卡牌生成詳細的RPG屬性，並存儲於 `cards.json` 文件中。此過程確保所有卡牌都能無縫對接到新的RPG系統。
*   **核心步驟**：
    1.  **數據提取**：從 `gacha_master_cards` 數據庫讀取所有卡牌的 `card_id`, `name`, `rarity` (名義稀有度), `pool_type` (卡池類型) 等基礎信息。
    2.  **技能庫準備**：確保 `active_skills.json`, `passive_skills.json`, 和 `innate_passive_skills.json` 文件已完整定義，包含所有技能的ID、效果、稀有度 (`skill_rarity`) 等。
    3.  **綜合強度評級 (CPR) 計算/指定**：
        *   **目的**：解決卡牌名義稀有度與實際獲取難度 (抽卡機率) 可能不符的問題。例如，一張獲取機率極低的R5卡，其RPG強度應高於普通R5，甚至可能媲美R6。
        *   **方法**：為每張 `card_id` 計算或手動指定一個CPR值（例如1-100）。此CPR值將作為RPG數值分配的主要依據，而非僅僅依賴名義稀有度。
    4.  **RPG數值預算模板定義**：
        *   基於CPR區間創建不同的預算模板。
        *   模板內容包括：基礎屬性總點數範圍、成長屬性總點數範圍、屬性分配傾向 (如坦克型、攻擊型)、通用被動技能槽數量範圍、以及可選的技能點數預算 (SPB) 用於"購買"高品質技能。
    5.  **屬性與技能分配 (對每張卡執行)**：
        *   根據卡牌的CPR選定預算模板。
        *   **基礎屬性與成長值**：在模板定義的總點數範圍內，結合屬性分配傾向，隨機分配各項基礎屬性和成長值。`pool_type` 可用於微調。
        *   **通用被動技能槽數量**：從模板範圍內隨機確定。
        *   **天賦被動技能 (`innate_passive_skill_id`)**：根據CPR和`pool_type` (主題匹配，可結合卡池優先池和標籤權重策略)，從 `innate_passive_skills.json` 中篩選與卡牌強度匹配的技能。篩選時主要參考天賦技能自身的 `skill_rarity`。若使用SPB，高稀有度技能消耗更多SPB。
        *   **普攻技能 (`primary_attack_skill_id`)**：類似天賦的分配方式 (可結合卡池優先池和標籤權重策略)，從 `active_skills.json` 選擇。普攻的傷害類型由其引用的技能定義決定，`cards.json` 中不包含 `primary_attack_damage_type` 字段。
        *   **預設主動/被動技能 (可選)**：若SPB有餘裕，可從 `active_skills.json` 和 `passive_skills.json` 中選擇與CPR匹配的技能預裝 (可結合卡池優先池和標籤權重策略)。
        *   **升星效果鍵 (`star_level_effects_key`) (可選)**：若有多套升星路徑，根據CPR分配，高CPR對應更優路徑。
        *   **卡池專屬內建效果 (可選)**：根據 `pool_type`，為卡牌添加微小的、符合主題的內建特性。例如，特定卡池的卡牌在對抗帶有特定標籤 (e.g., "UNDEAD") 的敵人時，獲得微小的傷害加成，或者對某類負面狀態效果 (e.g., "POISON_STATUS_ID") 有輕微的內建抵抗修正。
    6.  **數據格式化與驗證**：將生成的卡牌數據組織成 `cards.json` 要求的格式，並使用Pydantic模型進行驗證。
*   **關鍵考量**：
    *   **移除 `rpg_card_config_key`**：不再使用此字段，直接以 `card_id` 作為關聯鍵。
    *   **技能稀有度匹配**：卡牌的CPR應決定其能獲取的技能的稀有度範疇。高CPR卡牌應更有機會獲得高 `skill_rarity` 的技能。
    *   **迭代與平衡**：生成初始版本的 `cards.json` 後，需通過模擬和測試來驗證卡牌強度平衡性，並根據結果調整CPR計算、預算模板和技能分配邏輯。 