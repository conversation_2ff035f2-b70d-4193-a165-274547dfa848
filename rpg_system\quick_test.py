"""
RPG系統快速測試
驗證核心組件是否正常工作
"""

import sys
import os
import asyncio

# 添加項目根目錄到Python路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rpg_system.config.loader import <PERSON>fig<PERSON>oa<PERSON>
from rpg_system.formula_engine.evaluator import FormulaEvaluator
from rpg_system.battle_system.services.battle_service import BattleService
from rpg_system.battle_system.models.combatant import Combatant
from rpg_system.battle_system.models.skill_instance import SkillInstance, SkillType
import sys
import os
# 添加項目根目錄到Python路徑以訪問utils
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from utils.logger import logger


def create_test_combatant(instance_id: str, name: str, is_player_side: bool = True) -> Combatant:
    """創建測試戰鬥單位"""
    
    # 創建基本技能實例
    primary_attack = SkillInstance(
        skill_id="basic_attack",
        skill_type=SkillType.PRIMARY_ATTACK,
        current_level=1,
        current_cooldown=0
    )
    
    # 創建戰鬥單位
    combatant = Combatant(
        instance_id=instance_id,
        definition_id="test_card",
        name=name,
        is_player_side=is_player_side,
        rpg_level=1,
        star_level=0,
        skill_order_preference=["basic_attack"],
        primary_attack_skill=primary_attack,
        active_skills=[],
        innate_passive=None,
        common_passives=[],
        position=0
    )
    
    # 設置基礎屬性
    combatant.base_stats = {
        'max_hp': 100,
        'max_mp': 50,
        'patk': 20,
        'pdef': 15,
        'matk': 18,
        'mdef': 12,
        'spd': 10,
        'crit_rate': 0.05,
        'crit_dmg_multiplier': 1.5,
        'accuracy': 0.95,
        'evasion': 0.05,
        'mp_regen_per_turn': 0.0
    }
    
    # 設置當前屬性
    combatant.current_hp = 100
    combatant.max_hp = 100
    combatant.current_mp = 50
    combatant.max_mp = 50
    
    return combatant


async def test_config_loader():
    """測試配置加載器"""
    print("🔧 測試配置加載器...")
    
    try:
        config_loader = ConfigLoader()
        await config_loader.load_all_configs()
        
        # 檢查配置是否加載成功
        all_configs = config_loader.get_all_configs()
        
        if all_configs:
            print("   ✅ 配置加載成功")
            print(f"   📊 加載的配置類型: {list(all_configs.keys())}")
            return True
        else:
            print("   ❌ 配置為空")
            return False
            
    except Exception as e:
        print(f"   ❌ 配置加載失敗: {e}")
        return False


def test_formula_evaluator():
    """測試公式求值器"""
    print("🧮 測試公式求值器...")
    
    try:
        evaluator = FormulaEvaluator()
        
        # 測試基本公式
        result = evaluator.evaluate("2 + 3 * 4", {})
        
        if result == 14:
            print("   ✅ 公式求值正確")
            return True
        else:
            print(f"   ❌ 公式求值錯誤，期望14，得到{result}")
            return False
            
    except Exception as e:
        print(f"   ❌ 公式求值失敗: {e}")
        return False


async def test_battle_service():
    """測試戰鬥服務"""
    print("⚔️ 測試戰鬥服務...")
    
    try:
        # 創建配置加載器和公式求值器
        config_loader = ConfigLoader()
        await config_loader.load_all_configs()
        formula_evaluator = FormulaEvaluator()
        
        # 創建戰鬥服務
        battle_service = BattleService(config_loader, formula_evaluator)
        
        # 創建測試戰鬥單位
        player = create_test_combatant("player_1", "測試玩家", True)
        monster = create_test_combatant("monster_1", "測試怪物", False)
        
        # 創建戰鬥
        battle = battle_service.create_battle([player], [monster])
        
        if battle:
            print("   ✅ 戰鬥創建成功")
            print(f"   🆔 戰鬥ID: {battle.battle_id}")
            print(f"   👥 玩家隊伍: {len(battle.player_team)}")
            print(f"   👹 怪物隊伍: {len(battle.monster_team)}")
            return True
        else:
            print("   ❌ 戰鬥創建失敗")
            return False
            
    except Exception as e:
        print(f"   ❌ 戰鬥服務測試失敗: {e}")
        return False


async def test_single_battle_turn():
    """測試單個戰鬥回合"""
    print("🎯 測試單個戰鬥回合...")
    
    try:
        # 創建配置加載器和公式求值器
        config_loader = ConfigLoader()
        await config_loader.load_all_configs()
        formula_evaluator = FormulaEvaluator()
        
        # 創建戰鬥服務
        battle_service = BattleService(config_loader, formula_evaluator)
        
        # 創建測試戰鬥單位
        player = create_test_combatant("player_1", "測試玩家", True)
        monster = create_test_combatant("monster_1", "測試怪物", False)
        
        # 創建戰鬥
        battle = battle_service.create_battle([player], [monster])
        
        # 記錄初始狀態
        initial_player_hp = player.current_hp
        initial_monster_hp = monster.current_hp
        
        # 執行一個回合
        turn_result = battle_service.execute_battle_turn(battle)
        
        if turn_result['success']:
            print("   ✅ 回合執行成功")
            print(f"   💚 玩家HP: {initial_player_hp} -> {player.current_hp}")
            print(f"   💔 怪物HP: {initial_monster_hp} -> {monster.current_hp}")
            print(f"   📝 戰鬥日誌條目: {len(battle.battle_log)}")
            return True
        else:
            print(f"   ❌ 回合執行失敗: {turn_result.get('error', '未知錯誤')}")
            return False
            
    except Exception as e:
        print(f"   ❌ 戰鬥回合測試失敗: {e}")
        return False


async def test_event_system():
    """測試事件系統"""
    print("🎭 測試事件系統...")
    
    try:
        # 創建配置加載器和公式求值器
        config_loader = ConfigLoader()
        await config_loader.load_all_configs()
        formula_evaluator = FormulaEvaluator()
        
        # 創建戰鬥服務
        battle_service = BattleService(config_loader, formula_evaluator)
        
        # 創建測試戰鬥單位
        player = create_test_combatant("player_1", "測試玩家", True)
        monster = create_test_combatant("monster_1", "測試怪物", False)
        
        # 創建戰鬥
        battle = battle_service.create_battle([player], [monster])
        
        # 記錄初始日誌數量
        initial_log_count = len(battle.battle_log)
        
        # 手動觸發事件
        battle.trigger_event('ON_BATTLE_START', {
            'test_event': True,
            'player_team': [player.instance_id],
            'monster_team': [monster.instance_id]
        })
        
        # 檢查事件是否觸發
        if len(battle.battle_log) > initial_log_count:
            print("   ✅ 事件系統正常工作")
            print(f"   📝 事件觸發後日誌增加: {len(battle.battle_log) - initial_log_count}")
            return True
        else:
            print("   ❌ 事件系統未響應")
            return False
            
    except Exception as e:
        print(f"   ❌ 事件系統測試失敗: {e}")
        return False


async def run_quick_tests():
    """運行快速測試"""
    print("🧪 RPG系統快速測試")
    print("=" * 50)
    
    tests = [
        ("配置加載器", test_config_loader()),
        ("公式求值器", test_formula_evaluator()),
        ("戰鬥服務", test_battle_service()),
        ("戰鬥回合", test_single_battle_turn()),
        ("事件系統", test_event_system()),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_coro in tests:
        print(f"\n📋 {test_name}測試")
        print("-" * 30)
        
        try:
            if asyncio.iscoroutine(test_coro):
                result = await test_coro
            else:
                result = test_coro
            
            if result:
                passed += 1
                
        except Exception as e:
            print(f"   ❌ 測試異常: {e}")
            logger.error(f"{test_name}測試異常: {e}")
    
    # 輸出結果
    print("\n" + "=" * 50)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有快速測試通過！")
        return True
    else:
        print("⚠️ 部分測試失敗")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_quick_tests())
    print(f"\n{'✅ 測試成功' if success else '❌ 測試失敗'}")
