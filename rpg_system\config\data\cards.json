{"1": {"id": "1", "name": "火焰法師", "description": "火焰法師的RPG配置", "rarity": "SR", "card_type": "ATTACK", "base_stats": {"max_hp": 135.0, "max_mp": 75.0, "mp_regen_per_turn": 7.5, "patk": 39.0, "pdef": 20.25, "matk": 35.1, "mdef": 16.2, "spd": 16.5, "crit_rate": 0.05, "crit_dmg_multiplier": 1.5, "accuracy": 0.95, "evasion": 0.05}, "growth_per_rpg_level": {"max_hp": 6.75, "max_mp": 3.75, "mp_regen_per_turn": 0.375, "patk": 1.9500000000000002, "pdef": 1.0125, "matk": 1.7550000000000001, "mdef": 0.81, "spd": 0.8250000000000001, "crit_rate": 0.0, "crit_dmg_multiplier": 0.0, "accuracy": 0.0, "evasion": 0.0}, "star_level_effects_key": "star_effects_sr", "primary_attack_skill_id": "basic_attack", "innate_passive_skill_id": null, "available_active_skills": ["berserker_rage", "critical_strike", "power_strike", "basic_attack", "special_ability"], "max_rpg_level": 50, "rpg_unlock_requirements": {"min_star_level": 1, "required_items": []}}, "2": {"id": "2", "name": "聖騎士", "description": "聖騎士的RPG配置", "rarity": "SSR", "card_type": "DEFENSE", "base_stats": {"max_hp": 260.0, "max_mp": 100.0, "mp_regen_per_turn": 10.0, "patk": 32.0, "pdef": 42.0, "matk": 28.8, "mdef": 33.599999999999994, "spd": 16.0, "crit_rate": 0.05, "crit_dmg_multiplier": 1.5, "accuracy": 0.95, "evasion": 0.05}, "growth_per_rpg_level": {"max_hp": 13.0, "max_mp": 5.0, "mp_regen_per_turn": 0.5, "patk": 1.6, "pdef": 2.1, "matk": 1.****************, "mdef": 1.6799999999999997, "spd": 0.8, "crit_rate": 0.0, "crit_dmg_multiplier": 0.0, "accuracy": 0.0, "evasion": 0.0}, "star_level_effects_key": "star_effects_ssr", "primary_attack_skill_id": "basic_attack", "innate_passive_skill_id": null, "available_active_skills": ["shield_wall", "ultimate_skill", "power_strike", "taunt", "basic_attack", "special_ability"], "max_rpg_level": 50, "rpg_unlock_requirements": {"min_star_level": 1, "required_items": []}}, "3": {"id": "3", "name": "治療師", "description": "治療師的RPG配置", "rarity": "R", "card_type": "SUPPORT", "base_stats": {"max_hp": 120.0, "max_mp": 90.0, "mp_regen_per_turn": 9.0, "patk": 16.799999999999997, "pdef": 16.2, "matk": 25.919999999999998, "mdef": 17.279999999999998, "spd": 12.0, "crit_rate": 0.05, "crit_dmg_multiplier": 1.5, "accuracy": 0.95, "evasion": 0.05}, "growth_per_rpg_level": {"max_hp": 6.0, "max_mp": 4.5, "mp_regen_per_turn": 0.45, "patk": 0.8399999999999999, "pdef": 0.81, "matk": 1.296, "mdef": 0.8639999999999999, "spd": 0.6000000000000001, "crit_rate": 0.0, "crit_dmg_multiplier": 0.0, "accuracy": 0.0, "evasion": 0.0}, "star_level_effects_key": "star_effects_r", "primary_attack_skill_id": "basic_attack", "innate_passive_skill_id": null, "available_active_skills": ["buff_allies", "heal", "basic_attack", "power_strike"], "max_rpg_level": 50, "rpg_unlock_requirements": {"min_star_level": 1, "required_items": []}}, "4": {"id": "4", "name": "刺客", "description": "刺客的RPG配置", "rarity": "SR", "card_type": "ATTACK", "base_stats": {"max_hp": 135.0, "max_mp": 75.0, "mp_regen_per_turn": 7.5, "patk": 39.0, "pdef": 20.25, "matk": 35.1, "mdef": 16.2, "spd": 16.5, "crit_rate": 0.05, "crit_dmg_multiplier": 1.5, "accuracy": 0.95, "evasion": 0.05}, "growth_per_rpg_level": {"max_hp": 6.75, "max_mp": 3.75, "mp_regen_per_turn": 0.375, "patk": 1.9500000000000002, "pdef": 1.0125, "matk": 1.7550000000000001, "mdef": 0.81, "spd": 0.8250000000000001, "crit_rate": 0.0, "crit_dmg_multiplier": 0.0, "accuracy": 0.0, "evasion": 0.0}, "star_level_effects_key": "star_effects_sr", "primary_attack_skill_id": "basic_attack", "innate_passive_skill_id": null, "available_active_skills": ["berserker_rage", "critical_strike", "power_strike", "basic_attack", "special_ability"], "max_rpg_level": 50, "rpg_unlock_requirements": {"min_star_level": 1, "required_items": []}}, "5": {"id": "5", "name": "弓箭手", "description": "弓箭手的RPG配置", "rarity": "R", "card_type": "BALANCED", "base_stats": {"max_hp": 120.0, "max_mp": 60.0, "mp_regen_per_turn": 6.0, "patk": 24.0, "pdef": 18.0, "matk": 21.599999999999998, "mdef": 14.399999999999999, "spd": 12.0, "crit_rate": 0.05, "crit_dmg_multiplier": 1.5, "accuracy": 0.95, "evasion": 0.05}, "growth_per_rpg_level": {"max_hp": 6.0, "max_mp": 3.0, "mp_regen_per_turn": 0.30000000000000004, "patk": 1.2000000000000002, "pdef": 0.9, "matk": 1.0799999999999998, "mdef": 0.72, "spd": 0.6000000000000001, "crit_rate": 0.0, "crit_dmg_multiplier": 0.0, "accuracy": 0.0, "evasion": 0.0}, "star_level_effects_key": "star_effects_r", "primary_attack_skill_id": "basic_attack", "innate_passive_skill_id": null, "available_active_skills": ["basic_attack", "power_strike"], "max_rpg_level": 50, "rpg_unlock_requirements": {"min_star_level": 1, "required_items": []}}, "6": {"id": "6", "name": "龍騎士", "description": "龍騎士的RPG配置", "rarity": "UR", "card_type": "ATTACK", "base_stats": {"max_hp": 225.0, "max_mp": 125.0, "mp_regen_per_turn": 12.5, "patk": 65.0, "pdef": 33.75, "matk": 58.5, "mdef": 27.0, "spd": 27.500000000000004, "crit_rate": 0.05, "crit_dmg_multiplier": 1.5, "accuracy": 0.95, "evasion": 0.05}, "growth_per_rpg_level": {"max_hp": 11.25, "max_mp": 6.25, "mp_regen_per_turn": 0.625, "patk": 3.25, "pdef": 1.6875, "matk": 2.9250000000000003, "mdef": 1.35, "spd": 1.3750000000000002, "crit_rate": 0.0, "crit_dmg_multiplier": 0.0, "accuracy": 0.0, "evasion": 0.0}, "star_level_effects_key": "star_effects_ur", "primary_attack_skill_id": "basic_attack", "innate_passive_skill_id": null, "available_active_skills": ["berserker_rage", "ultimate_skill", "critical_strike", "power_strike", "basic_attack", "special_ability"], "max_rpg_level": 50, "rpg_unlock_requirements": {"min_star_level": 1, "required_items": []}}, "7": {"id": "7", "name": "魔法師", "description": "魔法師的RPG配置", "rarity": "SSR", "card_type": "ATTACK", "base_stats": {"max_hp": 180.0, "max_mp": 100.0, "mp_regen_per_turn": 10.0, "patk": 52.0, "pdef": 27.0, "matk": 46.800000000000004, "mdef": 21.6, "spd": 22.0, "crit_rate": 0.05, "crit_dmg_multiplier": 1.5, "accuracy": 0.95, "evasion": 0.05}, "growth_per_rpg_level": {"max_hp": 9.0, "max_mp": 5.0, "mp_regen_per_turn": 0.5, "patk": 2.6, "pdef": 1.35, "matk": 2.3400000000000003, "mdef": 1.08, "spd": 1.1, "crit_rate": 0.0, "crit_dmg_multiplier": 0.0, "accuracy": 0.0, "evasion": 0.0}, "star_level_effects_key": "star_effects_ssr", "primary_attack_skill_id": "basic_attack", "innate_passive_skill_id": null, "available_active_skills": ["berserker_rage", "ultimate_skill", "critical_strike", "power_strike", "basic_attack", "special_ability"], "max_rpg_level": 50, "rpg_unlock_requirements": {"min_star_level": 1, "required_items": []}}, "8": {"id": "8", "name": "盾衛", "description": "盾衛的RPG配置", "rarity": "R", "card_type": "DEFENSE", "base_stats": {"max_hp": 156.0, "max_mp": 60.0, "mp_regen_per_turn": 6.0, "patk": 19.200000000000003, "pdef": 25.2, "matk": 17.279999999999998, "mdef": 20.159999999999997, "spd": 9.600000000000001, "crit_rate": 0.05, "crit_dmg_multiplier": 1.5, "accuracy": 0.95, "evasion": 0.05}, "growth_per_rpg_level": {"max_hp": 7.800000000000001, "max_mp": 3.0, "mp_regen_per_turn": 0.30000000000000004, "patk": 0.9600000000000002, "pdef": 1.26, "matk": 0.8639999999999999, "mdef": 1.0079999999999998, "spd": 0.****************, "crit_rate": 0.0, "crit_dmg_multiplier": 0.0, "accuracy": 0.0, "evasion": 0.0}, "star_level_effects_key": "star_effects_r", "primary_attack_skill_id": "basic_attack", "innate_passive_skill_id": null, "available_active_skills": ["taunt", "shield_wall", "basic_attack", "power_strike"], "max_rpg_level": 50, "rpg_unlock_requirements": {"min_star_level": 1, "required_items": []}}, "9": {"id": "9", "name": "牧師", "description": "牧師的RPG配置", "rarity": "SR", "card_type": "SUPPORT", "base_stats": {"max_hp": 150.0, "max_mp": 112.5, "mp_regen_per_turn": 11.25, "patk": 21.0, "pdef": 20.25, "matk": 32.4, "mdef": 21.599999999999998, "spd": 15.0, "crit_rate": 0.05, "crit_dmg_multiplier": 1.5, "accuracy": 0.95, "evasion": 0.05}, "growth_per_rpg_level": {"max_hp": 7.5, "max_mp": 5.625, "mp_regen_per_turn": 0.5625, "patk": 1.05, "pdef": 1.0125, "matk": 1.62, "mdef": 1.0799999999999998, "spd": 0.75, "crit_rate": 0.0, "crit_dmg_multiplier": 0.0, "accuracy": 0.0, "evasion": 0.0}, "star_level_effects_key": "star_effects_sr", "primary_attack_skill_id": "basic_attack", "innate_passive_skill_id": null, "available_active_skills": ["heal", "power_strike", "buff_allies", "basic_attack", "special_ability"], "max_rpg_level": 50, "rpg_unlock_requirements": {"min_star_level": 1, "required_items": []}}, "10": {"id": "10", "name": "戰士", "description": "戰士的RPG配置", "rarity": "N", "card_type": "BALANCED", "base_stats": {"max_hp": 100.0, "max_mp": 50.0, "mp_regen_per_turn": 5.0, "patk": 20.0, "pdef": 15.0, "matk": 18.0, "mdef": 12.0, "spd": 10.0, "crit_rate": 0.05, "crit_dmg_multiplier": 1.5, "accuracy": 0.95, "evasion": 0.05}, "growth_per_rpg_level": {"max_hp": 5.0, "max_mp": 2.5, "mp_regen_per_turn": 0.25, "patk": 1.0, "pdef": 0.75, "matk": 0.9, "mdef": 0.6000000000000001, "spd": 0.5, "crit_rate": 0.0, "crit_dmg_multiplier": 0.0, "accuracy": 0.0, "evasion": 0.0}, "star_level_effects_key": "star_effects_n", "primary_attack_skill_id": "basic_attack", "innate_passive_skill_id": null, "available_active_skills": ["basic_attack"], "max_rpg_level": 50, "rpg_unlock_requirements": {"min_star_level": 1, "required_items": []}}}