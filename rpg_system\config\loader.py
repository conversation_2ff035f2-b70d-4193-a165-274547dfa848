"""
RPG配置加載器
負責在服務器啟動時加載所有JSON配置文件到內存
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
from utils.logger import logger


class ConfigLoader:
    """RPG配置文件加載器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置加載器
        
        Args:
            config_dir: 配置文件目錄路徑，默認為當前模組下的data目錄
        """
        if config_dir is None:
            config_dir = Path(__file__).parent / "data"
        
        self.config_dir = Path(config_dir)
        self._configs: Dict[str, Any] = {}
        self._loaded = False
    
    async def load_all_configs(self) -> Dict[str, Any]:
        """
        加載所有配置文件
        
        Returns:
            包含所有配置的字典
        """
        if self._loaded:
            return self._configs
        
        logger.info("開始加載RPG配置文件...")
        
        # 定義需要加載的配置文件
        config_files = [
            "cards.json",
            "active_skills.json", 
            "passive_skills.json",
            "innate_passive_skills.json",
            "effect_templates.json",
            "status_effects.json",
            "star_level_effects.json",
            "monsters.json",
            "floors.json",
            "monster_groups.json",
            "reward_packages.json"
        ]
        
        for config_file in config_files:
            config_name = config_file.replace('.json', '')
            try:
                config_data = await self._load_config_file(config_file)
                self._configs[config_name] = config_data
                logger.info(f"成功加載配置文件: {config_file}")
            except FileNotFoundError:
                logger.warning(f"配置文件不存在: {config_file}，將使用空配置")
                self._configs[config_name] = {}
            except Exception as e:
                logger.error(f"加載配置文件失敗: {config_file}, 錯誤: {e}")
                raise
        
        self._loaded = True
        logger.info("RPG配置文件加載完成")
        return self._configs

    def load_all_configs_sync(self) -> Dict[str, Any]:
        """
        同步加載所有配置文件

        Returns:
            包含所有配置的字典
        """
        if self._loaded:
            return self._configs

        logger.info("開始同步加載RPG配置文件...")

        # 配置文件列表
        config_files = [
            'cards.json',
            'active_skills.json',
            'passive_skills.json',
            'innate_passive_skills.json',
            'effect_templates.json',
            'status_effects.json',
            'star_level_effects.json',
            'monsters.json',
            'floors.json',
            'monster_groups.json',
            'reward_packages.json'
        ]

        for filename in config_files:
            config_name = filename.replace('.json', '')
            try:
                config_data = self._load_config_file_sync(filename)
                self._configs[config_name] = config_data
                logger.info(f"成功加載配置文件: {filename}")
            except FileNotFoundError:
                logger.warning(f"配置文件不存在: {filename}，將使用空配置")
                self._configs[config_name] = {}
            except Exception as e:
                logger.error(f"加載配置文件失敗: {filename}, 錯誤: {e}")
                self._configs[config_name] = {}

        self._loaded = True
        logger.info("RPG配置文件同步加載完成")
        return self._configs

    async def _load_config_file(self, filename: str) -> Dict[str, Any]:
        """
        加載單個配置文件
        
        Args:
            filename: 配置文件名
            
        Returns:
            配置數據字典
        """
        file_path = self.config_dir / filename
        
        if not file_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析錯誤 {filename}: {e}")
            raise
        except Exception as e:
            logger.error(f"讀取配置文件錯誤 {filename}: {e}")
            raise

    def _load_config_file_sync(self, filename: str) -> Dict[str, Any]:
        """
        同步加載單個配置文件

        Args:
            filename: 配置文件名

        Returns:
            配置數據字典
        """
        file_path = self.config_dir / filename

        if not file_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {file_path}")

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析錯誤 {filename}: {e}")
            raise
        except Exception as e:
            logger.error(f"讀取配置文件錯誤 {filename}: {e}")
            raise

    def get_config(self, config_name: str) -> Dict[str, Any]:
        """
        獲取指定的配置
        
        Args:
            config_name: 配置名稱（不含.json後綴）
            
        Returns:
            配置數據字典
        """
        if not self._loaded:
            raise RuntimeError("配置尚未加載，請先調用 load_all_configs()")
        
        return self._configs.get(config_name, {})
    
    def get_all_configs(self) -> Dict[str, Any]:
        """
        獲取所有配置
        
        Returns:
            包含所有配置的字典
        """
        if not self._loaded:
            raise RuntimeError("配置尚未加載，請先調用 load_all_configs()")
        
        return self._configs.copy()
    
    def reload_config(self, config_name: str) -> Dict[str, Any]:
        """
        重新加載指定配置文件

        Args:
            config_name: 配置名稱（不含.json後綴）

        Returns:
            重新加載的配置數據
        """
        filename = f"{config_name}.json"
        try:
            config_data = self._load_config_file_sync(filename)
            self._configs[config_name] = config_data
            logger.info(f"成功重新加載配置文件: {filename}")
            return config_data
        except Exception as e:
            logger.error(f"重新加載配置文件失敗: {filename}, 錯誤: {e}")
            raise

    def get_card_config(self, card_id: str) -> Optional[Any]:
        """
        獲取指定卡牌的配置

        Args:
            card_id: 卡牌ID

        Returns:
            卡牌配置對象或None
        """
        cards_config = self.get_config('cards')
        return cards_config.get(card_id)

    def get_monster_config(self, monster_id: str) -> Optional[Any]:
        """
        獲取指定怪物的配置

        Args:
            monster_id: 怪物ID

        Returns:
            怪物配置對象或None
        """
        monsters_config = self.get_config('monsters')
        return monsters_config.get(monster_id)

    def get_star_level_effects_config(self, effects_key: str) -> Optional[Any]:
        """
        獲取指定星級效果的配置

        Args:
            effects_key: 星級效果鍵值

        Returns:
            星級效果配置對象或None
        """
        star_effects_config = self.get_config('star_level_effects')
        return star_effects_config.get(effects_key)


# 全局配置加載器實例
_config_loader: Optional[ConfigLoader] = None


def get_config_loader() -> ConfigLoader:
    """獲取全局配置加載器實例"""
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader()
    return _config_loader


async def initialize_configs() -> Dict[str, Any]:
    """初始化並加載所有配置"""
    loader = get_config_loader()
    return await loader.load_all_configs()


def get_configs() -> Dict[str, Any]:
    """獲取所有已加載的配置"""
    loader = get_config_loader()
    return loader.get_all_configs()
