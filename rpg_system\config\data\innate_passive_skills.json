{"INNATE_BERSERKER_RAGE": {"name": "狂戰士之怒 (天賦)", "description_template": "當生命值低於 {50 - star_level * 0.5}% 時，物理攻擊力提升 {15 + star_level * 1.2}%", "skill_rarity": 4, "tags": ["BERSERKER", "PHYSICAL", "LOW_HP"], "base_effects": [{"trigger_condition": {"type": "ON_HP_THRESHOLD_REACHED", "chance_formula": "1.0", "trigger_once_per_battle": true, "params": {"threshold_percent_formula": "0.50 - star_level * 0.005", "check_direction": "BELOW"}}, "target_override": {"selector_type": "SELF", "max_targets": 1}, "effect_definitions": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "patk", "modification_type": "PERCENTAGE_ADD_BASE", "value_formula": "0.15 + star_level * 0.012"}]}]}], "milestone_effects": {"10": [{"trigger_condition": {"type": "ON_DAMAGE_DEALT", "sub_type": "PHYSICAL_DAMAGE", "chance_formula": "1.0", "trigger_once_per_battle": false, "additional_conditions": [{"source_combatant": "self", "check": "hp_below_percent", "value": 0.5}]}, "target_override": {"selector_type": "SELF", "max_targets": 1}, "effect_definitions": [{"effect_type": "HEAL", "heal_type": "PERCENT_DAMAGE_DEALT", "value_formula": "0.10 + (star_level - 10) * 0.005"}]}]}}}