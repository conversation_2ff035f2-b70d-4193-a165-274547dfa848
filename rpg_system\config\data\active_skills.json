{"basic_attack": {"name": "基礎攻擊", "description_template": "造成 {100 + skill_level * 5}% 物理攻擊力的傷害", "skill_rarity": 1, "max_level": 10, "target_type": "ENEMY_SINGLE", "base_mp_cost": 0, "mp_cost_per_level": 0, "base_cooldown_turns": 0, "cooldown_reduction_per_level": 0, "xp_gain_on_sacrifice": 10, "xp_to_next_level_config": {"base_xp": 50, "multiplier": 1.2}, "tags": ["PHYSICAL", "BASIC"], "base_effect_definitions": [{"effect_template": "BASIC_PHYSICAL_DAMAGE", "multiplier_formula": "1.0 + skill_level * 0.05", "can_crit": true}]}, "power_strike": {"name": "強力打擊", "description_template": "造成 {120 + skill_level * 15}% 物理攻擊力的傷害，消耗 {10 - skill_level * 0.5} MP", "skill_rarity": 2, "max_level": 8, "target_type": "ENEMY_SINGLE", "base_mp_cost": 10, "mp_cost_per_level": -0.5, "base_cooldown_turns": 2, "cooldown_reduction_per_level": 0, "xp_gain_on_sacrifice": 25, "xp_to_next_level_config": {"base_xp": 100, "multiplier": 1.3}, "tags": ["PHYSICAL", "POWER"], "base_effect_definitions": [{"effect_template": "BASIC_PHYSICAL_DAMAGE", "multiplier_formula": "1.2 + skill_level * 0.15", "can_crit": true}]}, "heal": {"name": "治療術", "description_template": "恢復友方 {100 + skill_level * 20}% 魔法攻擊力的生命值，消耗 {15 - skill_level * 0.8} MP", "skill_rarity": 2, "max_level": 10, "target_type": "ALLY_SINGLE", "base_mp_cost": 15, "mp_cost_per_level": -0.8, "base_cooldown_turns": 1, "cooldown_reduction_per_level": 0, "xp_gain_on_sacrifice": 20, "xp_to_next_level_config": {"base_xp": 80, "multiplier": 1.25}, "tags": ["MAGICAL", "HEAL"], "base_effect_definitions": [{"effect_type": "HEAL", "heal_type": "PERCENT_CASTER_MATK", "value_formula": "1.0 + skill_level * 0.2"}]}}