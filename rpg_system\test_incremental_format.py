#!/usr/bin/env python3
"""
測試增量式技能格式
"""

import sys
import os
# 添加項目根目錄到Python路徑以訪問utils
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from utils.logger import logger
from config.loader import ConfigLoader
from battle_system.models.skill_instance import SkillInstance, SkillType
from formula_engine.evaluator import FormulaEvaluator


def test_incremental_active_skills():
    """測試增量式主動技能格式"""
    print("🔧 測試增量式主動技能格式...")

    config_loader = ConfigLoader()
    # 使用同步方法加載配置
    all_configs = config_loader.load_all_configs_sync()
    
    # 測試基礎攻擊技能
    basic_attack = SkillInstance(
        skill_id="basic_attack",
        skill_type=SkillType.ACTIVE,
        current_level=1
    )
    
    definition_lv1 = basic_attack.get_definition(all_configs)
    print(f"   📊 基礎攻擊 Lv1: {definition_lv1.get('name', 'N/A')}")
    print(f"      MP消耗: {definition_lv1.get('mp_cost', 0)}")
    print(f"      冷卻: {definition_lv1.get('cooldown_turns', 0)}")
    
    # 測試等級5
    basic_attack.current_level = 5
    definition_lv5 = basic_attack.get_definition(all_configs)
    print(f"   📊 基礎攻擊 Lv5: {definition_lv5.get('name', 'N/A')}")
    print(f"      MP消耗: {definition_lv5.get('mp_cost', 0)}")
    print(f"      冷卻: {definition_lv5.get('cooldown_turns', 0)}")
    
    # 測試強力打擊技能
    power_strike = SkillInstance(
        skill_id="power_strike",
        skill_type=SkillType.ACTIVE,
        current_level=1
    )
    
    definition_ps1 = power_strike.get_definition(all_configs)
    print(f"   📊 強力打擊 Lv1: {definition_ps1.get('name', 'N/A')}")
    print(f"      MP消耗: {definition_ps1.get('mp_cost', 0)}")
    
    # 測試等級8
    power_strike.current_level = 8
    definition_ps8 = power_strike.get_definition(all_configs)
    print(f"   📊 強力打擊 Lv8: {definition_ps8.get('name', 'N/A')}")
    print(f"      MP消耗: {definition_ps8.get('mp_cost', 0)}")
    
    return True


def test_incremental_passive_skills():
    """測試增量式被動技能格式"""
    print("🔧 測試增量式被動技能格式...")

    config_loader = ConfigLoader()
    all_configs = config_loader.load_all_configs_sync()
    
    # 測試生命回復被動技能
    regen_skill = SkillInstance(
        skill_id="PASSIVE_REGEN",
        skill_type=SkillType.PASSIVE,
        current_level=1
    )
    
    definition_lv1 = regen_skill.get_definition(all_configs)
    print(f"   📊 生命回復 Lv1: {definition_lv1.get('name', 'N/A')}")
    print(f"      效果數量: {len(definition_lv1.get('effect_blocks', []))}")
    
    # 測試等級10
    regen_skill.current_level = 10
    definition_lv10 = regen_skill.get_definition(all_configs)
    print(f"   📊 生命回復 Lv10: {definition_lv10.get('name', 'N/A')}")
    print(f"      效果數量: {len(definition_lv10.get('effect_blocks', []))}")
    
    return True


def test_incremental_innate_passive_skills():
    """測試增量式天賦被動技能格式"""
    print("🔧 測試增量式天賦被動技能格式...")

    config_loader = ConfigLoader()
    all_configs = config_loader.load_all_configs_sync()
    
    # 測試狂戰士之怒天賦技能
    berserker_skill = SkillInstance(
        skill_id="INNATE_BERSERKER_RAGE",
        skill_type=SkillType.INNATE_PASSIVE,
        current_level=5  # 5星級
    )
    
    definition_star5 = berserker_skill.get_definition(all_configs)
    print(f"   📊 狂戰士之怒 5星: {definition_star5.get('name', 'N/A')}")
    print(f"      效果數量: {len(definition_star5.get('effect_blocks', []))}")
    
    # 測試15星級（應該包含里程碑效果）
    berserker_skill.current_level = 15
    definition_star15 = berserker_skill.get_definition(all_configs)
    print(f"   📊 狂戰士之怒 15星: {definition_star15.get('name', 'N/A')}")
    print(f"      效果數量: {len(definition_star15.get('effect_blocks', []))}")
    
    return True


def test_formula_evaluation_in_skills():
    """測試技能中的公式求值"""
    print("🔧 測試技能中的公式求值...")
    
    evaluator = FormulaEvaluator()
    
    # 測試增量式公式
    test_cases = [
        # 基礎攻擊傷害公式
        ("1.0 + skill_level * 0.05", {"skill_level": 1}, 1.05),
        ("1.0 + skill_level * 0.05", {"skill_level": 5}, 1.25),
        ("1.0 + skill_level * 0.05", {"skill_level": 10}, 1.5),
        
        # 被動技能回復公式
        ("0.03 + (skill_level - 1) * 0.01", {"skill_level": 1}, 0.03),
        ("0.03 + (skill_level - 1) * 0.01", {"skill_level": 5}, 0.07),
        ("0.03 + (skill_level - 1) * 0.01", {"skill_level": 10}, 0.12),
        
        # 天賦技能星級公式
        ("0.15 + star_level * 0.012", {"star_level": 0}, 0.15),
        ("0.15 + star_level * 0.012", {"star_level": 10}, 0.27),
        ("0.15 + star_level * 0.012", {"star_level": 25}, 0.45),
        
        # MP消耗公式
        ("10 - skill_level * 0.5", {"skill_level": 1}, 9.5),
        ("10 - skill_level * 0.5", {"skill_level": 8}, 6.0),
        
        # 反擊機率公式
        ("0.20 + skill_level * 0.03", {"skill_level": 1}, 0.23),
        ("0.20 + skill_level * 0.03", {"skill_level": 8}, 0.44),
    ]
    
    for formula, context, expected in test_cases:
        result = evaluator.evaluate(formula, context)
        status = "✅" if abs(result - expected) < 0.001 else "❌"
        print(f"   {status} {formula} = {result:.3f} (期望: {expected})")
    
    return True


def main():
    """主測試函數"""
    print("🧪 增量式技能格式測試")
    print("=" * 50)
    
    tests = [
        test_incremental_active_skills,
        test_incremental_passive_skills,
        test_incremental_innate_passive_skills,
        test_formula_evaluation_in_skills,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print("   ✅ 測試通過\n")
            else:
                print("   ❌ 測試失敗\n")
        except Exception as e:
            print(f"   ❌ 測試錯誤: {e}\n")
    
    print("=" * 50)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有增量式格式測試通過！")
        return True
    else:
        print("❌ 部分測試失敗")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
