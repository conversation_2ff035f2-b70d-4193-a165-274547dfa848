**III. 數據庫表結構**

本節描述與RPG系統相關的數據庫表結構。部分表已存在於現有系統中，針對這些表，我們會指出RPG功能所需的調整或新增欄位。其他則是RPG系統專用的新表。

### 1. `gacha_master_cards` (現有表)

*   **職責：** 存儲所有可抽取的卡牌的基礎靜態定義。`cards.json` 中的配置將直接使用本表的 `card_id` 作為主鍵進行關聯。
*   **`schema.sql` 中已有的核心字段 (部分，RPG相關或用於生成 `cards.json`)：**
    *   `card_id` (主鍵, 整數): 卡牌的唯一標識。
    *   `name` (字符串)
    *   `rarity` (整數, 1-7): 卡牌本身的稀有度。此稀有度將影響 `cards.json` 中對應卡牌的基礎屬性、成長潛力以及可獲得的天賦/技能稀有度範疇。
    *   `pool_type` (字符串, 例如 'main'): 卡池類型，也可能影響 `cards.json` 的生成規則。
    *   `sell_price` (整數): 卡牌的基礎售價。
    *   ... (其他現有展示性或基礎屬性)
*   **RPG系統的關聯：** 此表是 `cards.json` 的數據來源基礎，`cards.json` 會為此表中的每張卡牌擴展RPG特定屬性。

### 2. `gacha_user_collections` (現有表，需為RPG擴展)

*   **職責：** 存儲用戶擁有的卡牌實例。RPG系統將在此表基礎上增加卡牌的RPG狀態和技能裝配信息。
*   **`schema.sql` 中已有的核心字段：**
    *   `id` (主鍵, 整數)
    *   `user_id` (外鍵, 指向 `gacha_users.user_id`)
    *   `card_id` (外鍵, 指向 `gacha_master_cards.card_id`)
    *   `quantity` (整數, 默認1)
    *   `first_acquired` (時間戳)
    *   `last_acquired` (時間戳)
    *   `is_favorite` (布爾值, 默認 false): 標記卡牌是否為用戶的最愛，用於在批量獻祭等操作中進行保護。
    *   `star_level` (整數, 默認0, 範圍 0-35): 卡牌的「培養星級」。在RPG系統中，此字段直接決定卡牌的天賦效果檔位 (對應 `innate_passive_skills.json` 的 `effects_by_star_level`) 和 `star_level_effects.json` 的加成。
    *   `custom_sort_index` (長整型, 可選)
*   **RPG系統需在此表新增/確認的字段：**
    *   `rpg_level` (整數, 默認1): 卡牌的RPG等級，通過戰鬥經驗提升。
    *   `rpg_xp` (整數, 默認0): 卡牌當前RPG等級的經驗值。
    *   `equipped_active_skill_ids` (JSON數組 of 字符串, 例如 `["skill_X_id", "skill_Y_id", null]`): 裝備的通用主動技能的 `skill_id`。數組的順序代表玩家設定的技能釋放優先級。數組長度應與卡牌可裝備的主動技能槽數量一致。技能生效等級由 `gacha_user_learned_global_skills` 中對應的全局熟練度等級決定。
    *   `equipped_common_passives` (JSONB, 例如 `{"slot_0": "passive_A_id", "slot_1": "passive_B_id", "slot_2": null}`). 存儲已裝備的通用被動技能的 `skill_id`。鍵為槽位索引 (0 至 `cards.json` 中定義的 `passive_skill_slots - 1`)，值為 `passive_skills.json` 中的 `skill_id` 或 `null`。技能生效等級由 `gacha_user_learned_global_skills` 中對應的全局熟練度等級決定。

### 3. `gacha_user_learned_global_skills` (RPG系統需創建的新表)

*   **職責：** 存儲用戶對各個通用主動技能和通用被動技能的「全局熟練度等級」。當卡牌裝備了某個 `skill_id` 的技能時，其在戰鬥中生效的等級將由此處定義的全局熟練度等級決定。
*   **狀態：** 此表定義於RPG設計文檔中，但**目前不存在于 `schema.sql` 中，需要後續創建。**
*   **主鍵：** (`user_id`, `skill_id`, `skill_type`)
*   **字段：**
    *   `user_id` (主鍵部分, 外鍵, 指向 `gacha_users.user_id`)
    *   `skill_id` (主鍵部分, 字符串, 指向 `active_skills.json` 或 `passive_skills.json` 的鍵)
    *   `skill_type` (主鍵部分, 枚舉: "ACTIVE", "PASSIVE")
    *   `skill_level` (整數, 默認1): 玩家對該 `skill_id` 的全局熟練度等級。
    *   `skill_xp` (整數, 默認0): 該全局熟練度等級當前獲得的經驗值。
    *   `unlocked_at` (時間戳, 默認 CURRENT_TIMESTAMP): 玩家首次記錄該技能熟練度的時間。

### 4. `rpg_user_progress` (RPG系統需創建的新表)

*   **職責：** 存儲用戶在RPG PVE模式下的進度。
*   **狀態：** 此表定義於RPG設計文檔中，但**目前不存在于 `schema.sql` 中，需要後續創建。**
*   **主鍵：** `user_id`
*   **字段：**
    *   `user_id` (主鍵, 外鍵, 指向 `gacha_users.user_id`)
    *   `current_floor_unlocked` (整數, 默認1): 玩家當前已解鎖到的最高樓層。
    *   `current_floor_wins` (整數, 默認0): 玩家在當前已解鎖樓層的勝利次數 (用於判斷是否可晉級)。
    *   `max_floor_cleared` (整數, 默認0): 玩家歷史上成功通關的最高樓層。
    *   `current_team_formation` (JSONB, 可選): 存儲玩家當前PVE的出戰隊伍配置。
        *   例如: `{"team_name": "Default", "slots": [{"card_collection_id": 123, "position": 0}, {"card_collection_id": 456, "position": 1}]}`
        *   或者更簡單的 `[123, 456]` (按順序的 `gacha_user_collections.id` 列表)

---
(原有 `gacha_user_collections` 的描述已合併更新至上方，此處舊描述可移除或根據實際情況調整) 