"""
主動技能配置的Pydantic模型
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator

from .base_models import EffectDefinition, TargetLogicDetail


class ActiveSkillEffectLevel(BaseModel):
    """主動技能效果等級模型（舊格式）"""
    mp_cost: int = Field(..., ge=0)
    cooldown_turns: int = Field(..., ge=0)
    effect_definitions: List[EffectDefinition]


class XpToNextLevelConfig(BaseModel):
    """技能升級XP配置模型"""
    base_xp: int = Field(..., ge=1)
    multiplier: float = Field(..., gt=1.0)


class ActiveSkillConfig(BaseModel):
    """主動技能配置模型（支持新舊格式）"""
    name: str
    description_template: str
    skill_rarity: int = Field(..., ge=1, le=7)
    max_level: int = Field(..., ge=1)
    target_type: str
    target_logic_details: Optional[List[TargetLogicDetail]] = None

    # 舊格式：按等級分組
    effects_by_level: Optional[Dict[str, ActiveSkillEffectLevel]] = None

    # 新格式：增量式
    base_mp_cost: Optional[int] = Field(None, ge=0)
    mp_cost_per_level: Optional[float] = None
    base_cooldown_turns: Optional[int] = Field(None, ge=0)
    cooldown_reduction_per_level: Optional[float] = None
    base_effect_definitions: Optional[List[EffectDefinition]] = None

    # 共同字段
    xp_gain_on_sacrifice: Optional[int] = Field(None, ge=0)
    xp_to_next_level_config: Optional[XpToNextLevelConfig] = None
    tags: Optional[List[str]] = None

    @validator('effects_by_level')
    def check_level_keys_are_positive_numeric_strings(cls, v):
        if not v:
            raise ValueError("effects_by_level cannot be empty.")
        for key in v.keys():
            if not key.isdigit() or int(key) <= 0:
                raise ValueError(f"Level key '{key}' in effects_by_level must be a positive numeric string (e.g., \"1\", \"10\").")
        return v

    @validator('target_logic_details', pre=True, always=True)
    def sort_target_logic_details_by_priority(cls, v):
        if v:
            # Ensure it's a list of dicts or TargetLogicDetail objects before sorting
            if all(isinstance(item, dict) for item in v):
                v.sort(key=lambda x: x.get('priority_score', 0), reverse=True)
            elif all(isinstance(item, TargetLogicDetail) for item in v):
                v.sort(key=lambda x: x.priority_score, reverse=True)
        return v


class ActiveSkillsConfig(BaseModel):
    """主動技能配置文件模型"""
    __root__: Dict[str, ActiveSkillConfig]
    
    def __iter__(self):
        return iter(self.__root__)
    
    def __getitem__(self, item):
        return self.__root__[item]
    
    def get(self, key, default=None):
        return self.__root__.get(key, default)
    
    def keys(self):
        return self.__root__.keys()
    
    def values(self):
        return self.__root__.values()
    
    def items(self):
        return self.__root__.items()
