"""
RPG配置生成腳本
從現有的卡牌數據生成RPG系統所需的配置文件
"""

import sys
import os
import json
import asyncio
from typing import Dict, Any, List, Optional

# 添加項目根目錄到Python路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 暫時註釋掉gacha依賴，使用模擬數據
# from gacha.repositories.card.card_repository import CardRepository
# from gacha.repositories.card.card_master_repository import CardMasterRepository
from utils.logger import logger


class RPGConfigGenerator:
    """RPG配置生成器"""
    
    def __init__(self):
        """初始化生成器"""
        # 暫時使用模擬數據，不依賴gacha模組
        # self.card_repo = CardRepository()
        # self.card_master_repo = CardMasterRepository()
        self.output_dir = "config/data"

        # 確保輸出目錄存在
        os.makedirs(self.output_dir, exist_ok=True)

    def _create_mock_cards(self) -> List[Dict[str, Any]]:
        """創建模擬卡牌數據"""
        return [
            {'id': 1, 'name': '火焰法師', 'rarity': 'SR', 'card_type': 'ATTACK'},
            {'id': 2, 'name': '聖騎士', 'rarity': 'SSR', 'card_type': 'DEFENSE'},
            {'id': 3, 'name': '治療師', 'rarity': 'R', 'card_type': 'SUPPORT'},
            {'id': 4, 'name': '刺客', 'rarity': 'SR', 'card_type': 'ATTACK'},
            {'id': 5, 'name': '弓箭手', 'rarity': 'R', 'card_type': 'BALANCED'},
            {'id': 6, 'name': '龍騎士', 'rarity': 'UR', 'card_type': 'ATTACK'},
            {'id': 7, 'name': '魔法師', 'rarity': 'SSR', 'card_type': 'ATTACK'},
            {'id': 8, 'name': '盾衛', 'rarity': 'R', 'card_type': 'DEFENSE'},
            {'id': 9, 'name': '牧師', 'rarity': 'SR', 'card_type': 'SUPPORT'},
            {'id': 10, 'name': '戰士', 'rarity': 'N', 'card_type': 'BALANCED'},
        ]
    
    async def generate_card_configs(self) -> bool:
        """生成卡牌RPG配置"""
        try:
            print("🃏 生成卡牌RPG配置...")

            # 使用模擬卡牌數據
            mock_cards = self._create_mock_cards()

            if not mock_cards:
                print("❌ 沒有找到卡牌數據")
                return False

            card_configs = {}

            for card in mock_cards:
                card_config = self._generate_single_card_config(card)
                card_configs[str(card['id'])] = card_config
            
            # 保存配置文件
            config_path = os.path.join(self.output_dir, "cards.json")
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(card_configs, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 生成了 {len(card_configs)} 個卡牌配置")
            print(f"   保存到: {config_path}")
            return True
            
        except Exception as e:
            print(f"❌ 生成卡牌配置失敗: {e}")
            logger.error(f"生成卡牌配置失敗: {e}")
            return False
    
    def _generate_single_card_config(self, card: Dict[str, Any]) -> Dict[str, Any]:
        """生成單個卡牌的RPG配置"""

        # 根據稀有度設置基礎屬性
        rarity_stats = self._get_rarity_base_stats(card['rarity'])

        # 根據卡牌類型調整屬性
        type_modifier = self._get_type_modifier(card.get('card_type', 'NORMAL'))

        # 計算最終基礎屬性
        base_stats = {}
        for stat, value in rarity_stats.items():
            base_stats[stat] = value * type_modifier.get(stat, 1.0)

        return {
            "id": str(card['id']),
            "name": card['name'],
            "description": card.get('description', f"{card['name']}的RPG配置"),
            "rarity": card['rarity'],
            "card_type": card.get('card_type', 'NORMAL'),
            "base_stats": base_stats,
            "growth_per_rpg_level": self._generate_growth_stats(base_stats),
            "star_level_effects_key": f"star_effects_{card['rarity'].lower()}",
            "primary_attack_skill_id": "basic_attack",
            "innate_passive_skill_id": None,  # 可以根據卡牌特性設置
            "available_active_skills": self._generate_available_skills(card),
            "max_rpg_level": 50,
            "rpg_unlock_requirements": {
                "min_star_level": 1,
                "required_items": []
            }
        }
    
    def _get_rarity_base_stats(self, rarity: str) -> Dict[str, float]:
        """根據稀有度獲取基礎屬性"""
        rarity_multipliers = {
            'N': 1.0,
            'R': 1.2,
            'SR': 1.5,
            'SSR': 2.0,
            'UR': 2.5
        }
        
        multiplier = rarity_multipliers.get(rarity, 1.0)
        
        return {
            "max_hp": 100.0 * multiplier,
            "max_mp": 50.0 * multiplier,
            "mp_regen_per_turn": 5.0 * multiplier,
            "patk": 20.0 * multiplier,
            "pdef": 15.0 * multiplier,
            "matk": 18.0 * multiplier,
            "mdef": 12.0 * multiplier,
            "spd": 10.0 * multiplier,
            "crit_rate": 0.05,
            "crit_dmg_multiplier": 1.5,
            "accuracy": 0.95,
            "evasion": 0.05
        }
    
    def _get_type_modifier(self, card_type: str) -> Dict[str, float]:
        """根據卡牌類型獲取屬性修正"""
        type_modifiers = {
            'ATTACK': {
                'patk': 1.3,
                'matk': 1.3,
                'max_hp': 0.9,
                'pdef': 0.9,
                'mdef': 0.9,
                'spd': 1.1
            },
            'DEFENSE': {
                'pdef': 1.4,
                'mdef': 1.4,
                'max_hp': 1.3,
                'patk': 0.8,
                'matk': 0.8,
                'spd': 0.8
            },
            'SUPPORT': {
                'max_mp': 1.5,
                'mp_regen_per_turn': 1.5,
                'matk': 1.2,
                'mdef': 1.2,
                'patk': 0.7,
                'pdef': 0.9
            },
            'BALANCED': {
                # 平衡型無修正
            },
            'NORMAL': {
                # 普通型無修正
            }
        }
        
        return type_modifiers.get(card_type, {})
    
    def _generate_growth_stats(self, base_stats: Dict[str, float]) -> Dict[str, float]:
        """生成等級成長屬性"""
        growth_stats = {}
        
        for stat, base_value in base_stats.items():
            if stat in ['crit_rate', 'crit_dmg_multiplier', 'accuracy', 'evasion']:
                # 這些屬性不隨等級成長
                growth_stats[stat] = 0.0
            else:
                # 其他屬性按基礎值的5%成長
                growth_stats[stat] = base_value * 0.05
        
        return growth_stats
    
    def _generate_available_skills(self, card: Dict[str, Any]) -> List[str]:
        """生成可用技能列表"""
        # 根據卡牌稀有度和類型生成可用技能
        available_skills = ["basic_attack"]  # 所有卡牌都有基礎攻擊

        rarity = card['rarity']
        card_type = card.get('card_type', 'NORMAL')
        
        # 根據稀有度添加技能
        if rarity in ['R', 'SR', 'SSR', 'UR']:
            available_skills.append("power_strike")
        
        if rarity in ['SR', 'SSR', 'UR']:
            available_skills.append("special_ability")
        
        if rarity in ['SSR', 'UR']:
            available_skills.append("ultimate_skill")
        
        # 根據類型添加特殊技能
        if card_type == 'ATTACK':
            available_skills.extend(["berserker_rage", "critical_strike"])
        elif card_type == 'DEFENSE':
            available_skills.extend(["shield_wall", "taunt"])
        elif card_type == 'SUPPORT':
            available_skills.extend(["heal", "buff_allies"])
        
        return list(set(available_skills))  # 去重
    
    def generate_basic_skills(self) -> bool:
        """生成基礎技能配置"""
        try:
            print("⚔️ 生成基礎技能配置...")
            
            skills = {
                "basic_attack": {
                    "name": "基礎攻擊",
                    "description_template": "造成 {100 + skill_level * 5}% 物理攻擊力的傷害",
                    "skill_rarity": 1,
                    "max_level": 10,
                    "target_type": "ENEMY_SINGLE",
                    "base_mp_cost": 0,
                    "mp_cost_per_level": 0,
                    "base_cooldown_turns": 0,
                    "cooldown_reduction_per_level": 0,
                    "xp_gain_on_sacrifice": 10,
                    "xp_to_next_level_config": {
                        "base_xp": 50,
                        "multiplier": 1.2
                    },
                    "tags": ["PHYSICAL", "BASIC"],
                    "base_effect_definitions": [
                        {
                            "effect_template": "BASIC_PHYSICAL_DAMAGE",
                            "multiplier_formula": "1.0 + skill_level * 0.05",
                            "can_crit": True
                        }
                    ]
                },
                "power_strike": {
                    "name": "強力打擊",
                    "description_template": "造成 {120 + skill_level * 15}% 物理攻擊力的傷害，消耗 {10 - skill_level * 0.5} MP",
                    "skill_rarity": 2,
                    "max_level": 8,
                    "target_type": "ENEMY_SINGLE",
                    "base_mp_cost": 10,
                    "mp_cost_per_level": -0.5,
                    "base_cooldown_turns": 2,
                    "cooldown_reduction_per_level": 0,
                    "xp_gain_on_sacrifice": 25,
                    "xp_to_next_level_config": {
                        "base_xp": 100,
                        "multiplier": 1.3
                    },
                    "tags": ["PHYSICAL", "POWER"],
                    "base_effect_definitions": [
                        {
                            "effect_template": "BASIC_PHYSICAL_DAMAGE",
                            "multiplier_formula": "1.2 + skill_level * 0.15",
                            "can_crit": True
                        }
                    ]
                },
                "heal": {
                    "name": "治療術",
                    "description_template": "恢復友方 {100 + skill_level * 20}% 魔法攻擊力的生命值，消耗 {15 - skill_level * 0.8} MP",
                    "skill_rarity": 2,
                    "max_level": 10,
                    "target_type": "ALLY_SINGLE",
                    "base_mp_cost": 15,
                    "mp_cost_per_level": -0.8,
                    "base_cooldown_turns": 1,
                    "cooldown_reduction_per_level": 0,
                    "xp_gain_on_sacrifice": 20,
                    "xp_to_next_level_config": {
                        "base_xp": 80,
                        "multiplier": 1.25
                    },
                    "tags": ["MAGICAL", "HEAL"],
                    "base_effect_definitions": [
                        {
                            "effect_type": "HEAL",
                            "heal_type": "PERCENT_CASTER_MATK",
                            "value_formula": "1.0 + skill_level * 0.2"
                        }
                    ]
                }
            }
            
            # 保存技能配置
            skills_path = os.path.join(self.output_dir, "active_skills.json")
            with open(skills_path, 'w', encoding='utf-8') as f:
                json.dump(skills, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 生成了 {len(skills)} 個基礎技能")
            print(f"   保存到: {skills_path}")
            return True
            
        except Exception as e:
            print(f"❌ 生成基礎技能失敗: {e}")
            logger.error(f"生成基礎技能失敗: {e}")
            return False
    
    def generate_star_effects(self) -> bool:
        """生成星級效果配置"""
        try:
            print("⭐ 生成星級效果配置...")
            
            star_effects = {}
            
            # 為每個稀有度生成星級效果
            rarities = ['n', 'r', 'sr', 'ssr', 'ur']
            
            for rarity in rarities:
                effects = {}
                
                # 每個星級的效果
                for star in range(1, 11):  # 1-10星
                    effects[str(star)] = {
                        "attribute_modifiers": [
                            {
                                "attribute": "patk",
                                "modification_type": "PERCENTAGE_ADD_BASE",
                                "value": star * 0.05  # 每星增加5%
                            },
                            {
                                "attribute": "matk",
                                "modification_type": "PERCENTAGE_ADD_BASE",
                                "value": star * 0.05
                            },
                            {
                                "attribute": "max_hp",
                                "modification_type": "PERCENTAGE_ADD_BASE",
                                "value": star * 0.03  # 每星增加3%
                            }
                        ],
                        "unlocked_passive_slots": min(star // 2, 3),  # 每2星解鎖一個被動槽，最多3個
                        "description": f"{star}星效果：攻擊力+{star*5}%，生命值+{star*3}%"
                    }
                
                star_effects[f"star_effects_{rarity}"] = effects
            
            # 保存星級效果配置
            star_path = os.path.join(self.output_dir, "star_level_effects.json")
            with open(star_path, 'w', encoding='utf-8') as f:
                json.dump(star_effects, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 生成了 {len(rarities)} 個稀有度的星級效果")
            print(f"   保存到: {star_path}")
            return True
            
        except Exception as e:
            print(f"❌ 生成星級效果失敗: {e}")
            logger.error(f"生成星級效果失敗: {e}")
            return False
    
    def generate_test_monsters(self) -> bool:
        """生成測試怪物配置"""
        try:
            print("👹 生成測試怪物配置...")
            
            monsters = {
                "goblin": {
                    "id": "goblin",
                    "name": "哥布林",
                    "description": "弱小的綠皮怪物",
                    "level": 1,
                    "base_stats": {
                        "max_hp": 60.0,
                        "max_mp": 20.0,
                        "mp_regen_per_turn": 2.0,
                        "patk": 12.0,
                        "pdef": 8.0,
                        "matk": 8.0,
                        "mdef": 6.0,
                        "spd": 8.0,
                        "crit_rate": 0.03,
                        "crit_dmg_multiplier": 1.3,
                        "accuracy": 0.90,
                        "evasion": 0.08
                    },
                    "primary_attack_skill_id": "basic_attack",
                    "active_skill_order": ["basic_attack"],
                    "equipped_passives": []
                },
                "orc_warrior": {
                    "id": "orc_warrior",
                    "name": "獸人戰士",
                    "description": "強壯的獸人戰士",
                    "level": 5,
                    "base_stats": {
                        "max_hp": 120.0,
                        "max_mp": 30.0,
                        "mp_regen_per_turn": 3.0,
                        "patk": 25.0,
                        "pdef": 18.0,
                        "matk": 12.0,
                        "mdef": 15.0,
                        "spd": 6.0,
                        "crit_rate": 0.05,
                        "crit_dmg_multiplier": 1.5,
                        "accuracy": 0.92,
                        "evasion": 0.03
                    },
                    "primary_attack_skill_id": "basic_attack",
                    "active_skill_order": ["power_strike", "basic_attack"],
                    "equipped_passives": []
                }
            }
            
            # 保存怪物配置
            monsters_path = os.path.join(self.output_dir, "monsters.json")
            with open(monsters_path, 'w', encoding='utf-8') as f:
                json.dump(monsters, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 生成了 {len(monsters)} 個測試怪物")
            print(f"   保存到: {monsters_path}")
            return True
            
        except Exception as e:
            print(f"❌ 生成測試怪物失敗: {e}")
            logger.error(f"生成測試怪物失敗: {e}")
            return False


async def main():
    """主函數"""
    print("🏗️ RPG配置生成腳本")
    print("=" * 50)
    
    generator = RPGConfigGenerator()
    
    success_count = 0
    total_tasks = 4
    
    # 生成各種配置
    tasks = [
        ("卡牌配置", generator.generate_card_configs()),
        ("基礎技能", generator.generate_basic_skills()),
        ("星級效果", generator.generate_star_effects()),
        ("測試怪物", generator.generate_test_monsters())
    ]
    
    for task_name, task_coro in tasks:
        print(f"\n📋 {task_name}...")
        try:
            if isinstance(task_coro, bool):
                # 同步任務
                result = task_coro
            else:
                # 異步任務
                result = await task_coro
            
            if result:
                success_count += 1
        except Exception as e:
            print(f"❌ {task_name}失敗: {e}")
            logger.error(f"{task_name}失敗: {e}")
    
    # 輸出結果
    print("\n" + "=" * 50)
    print(f"配置生成結果: {success_count}/{total_tasks} 成功")
    
    if success_count == total_tasks:
        print("🎉 所有配置生成完成！")
        print(f"📁 配置文件保存在: {generator.output_dir}")
    else:
        print("⚠️ 部分配置生成失敗")


if __name__ == "__main__":
    asyncio.run(main())
