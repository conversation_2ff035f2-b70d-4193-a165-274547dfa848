{"PASSIVE_REGEN": {"name": "生命回復", "description_template": "每回合開始時回復 {base_value + (skill_level - 1) * increment_value}% 最大生命值", "skill_rarity": 2, "max_level": 10, "xp_gain_on_sacrifice": 15, "xp_to_next_level_config": {"base_xp": 100, "multiplier": 1.5}, "tags": ["HEALING", "SUSTAIN"], "base_effects": [{"trigger_condition": {"type": "ON_TURN_START", "chance_formula": "1.0", "trigger_once_per_battle": false}, "target_override": {"selector_type": "SELF", "max_targets": 1}, "effect_definitions": [{"effect_type": "HEAL", "heal_type": "PERCENT_MAX_HP", "value_formula": "0.03 + (skill_level - 1) * 0.01"}]}]}, "PASSIVE_COUNTER_ATTACK": {"name": "反擊", "description_template": "受到物理傷害時有 {20 + skill_level * 3}% 機率反擊，造成 {60 + skill_level * 8}% 物理攻擊力的傷害", "skill_rarity": 3, "max_level": 8, "xp_gain_on_sacrifice": 25, "xp_to_next_level_config": {"base_xp": 150, "multiplier": 1.6}, "tags": ["COUNTER", "PHYSICAL"], "base_effects": [{"trigger_condition": {"type": "ON_DAMAGE_TAKEN", "sub_type": "PHYSICAL_DAMAGE", "chance_formula": "0.20 + skill_level * 0.03", "trigger_once_per_battle": false, "additional_conditions": [{"source_combatant": "self", "check": "hp_above_percent", "value": 0.1}]}, "target_override": {"selector_type": "ATTACKER", "max_targets": 1}, "effect_definitions": [{"effect_template": "BASIC_PHYSICAL_DAMAGE", "multiplier_formula": "0.60 + skill_level * 0.08", "can_crit_formula": "if(skill_level >= 5, 1, 0)"}]}]}, "PASSIVE_CRIT_BOOST": {"name": "暴擊強化", "description_template": "永久增加 {3 + skill_level * 1}% 暴擊率", "skill_rarity": 2, "max_level": 10, "xp_gain_on_sacrifice": 20, "xp_to_next_level_config": {"base_xp": 120, "multiplier": 1.4}, "tags": ["CRIT", "PASSIVE_STAT"], "base_effects": [{"trigger_condition": {"type": "ON_BATTLE_START", "chance_formula": "1.0", "trigger_once_per_battle": true}, "target_override": {"selector_type": "SELF", "max_targets": 1}, "effect_definitions": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "crit_rate", "modification_type": "FLAT_ADD", "value_formula": "0.03 + skill_level * 0.01"}]}]}]}}