{"PASSIVE_REGEN": {"name": "生命回復", "description_template": "每回合開始時回復 {value}% 最大生命值", "skill_rarity": 2, "max_level": 10, "xp_to_next_level_config": {"base_xp": 100, "multiplier": 1.5}, "tags": ["HEALING", "SUSTAIN"], "effects_by_level": {"1": [{"trigger_condition": {"type": "ON_TURN_START", "chance_formula": "1.0", "trigger_once_per_battle": false}, "target_override": {"selector_type": "SELF", "max_targets": 1}, "effect_definitions": [{"effect_type": "HEAL", "heal_type": "PERCENT_MAX_HP", "value": 0.05}]}], "5": [{"trigger_condition": {"type": "ON_TURN_START", "chance_formula": "1.0", "trigger_once_per_battle": false}, "target_override": {"selector_type": "SELF", "max_targets": 1}, "effect_definitions": [{"effect_type": "HEAL", "heal_type": "PERCENT_MAX_HP", "value": 0.08}]}], "10": [{"trigger_condition": {"type": "ON_TURN_START", "chance_formula": "1.0", "trigger_once_per_battle": false}, "target_override": {"selector_type": "SELF", "max_targets": 1}, "effect_definitions": [{"effect_type": "HEAL", "heal_type": "PERCENT_MAX_HP", "value": 0.12}]}]}}, "PASSIVE_COUNTER_ATTACK": {"name": "反擊", "description_template": "受到物理傷害時有 {chance}% 機率反擊", "skill_rarity": 3, "max_level": 8, "xp_to_next_level_config": {"base_xp": 150, "multiplier": 1.6}, "tags": ["COUNTER", "PHYSICAL"], "effects_by_level": {"1": [{"trigger_condition": {"type": "ON_DAMAGE_TAKEN", "sub_type": "PHYSICAL_DAMAGE", "chance_formula": "0.25", "trigger_once_per_battle": false, "additional_conditions": [{"source_combatant": "self", "check": "hp_above_percent", "value": 0.1}]}, "target_override": {"selector_type": "ATTACKER", "max_targets": 1}, "effect_definitions": [{"effect_template": "BASIC_PHYSICAL_DAMAGE", "multiplier": 0.8, "can_crit": false}]}], "4": [{"trigger_condition": {"type": "ON_DAMAGE_TAKEN", "sub_type": "PHYSICAL_DAMAGE", "chance_formula": "0.35", "trigger_once_per_battle": false, "additional_conditions": [{"source_combatant": "self", "check": "hp_above_percent", "value": 0.1}]}, "target_override": {"selector_type": "ATTACKER", "max_targets": 1}, "effect_definitions": [{"effect_template": "BASIC_PHYSICAL_DAMAGE", "multiplier": 1.0, "can_crit": false}]}], "8": [{"trigger_condition": {"type": "ON_DAMAGE_TAKEN", "sub_type": "PHYSICAL_DAMAGE", "chance_formula": "0.45", "trigger_once_per_battle": false, "additional_conditions": [{"source_combatant": "self", "check": "hp_above_percent", "value": 0.1}]}, "target_override": {"selector_type": "ATTACKER", "max_targets": 1}, "effect_definitions": [{"effect_template": "BASIC_PHYSICAL_DAMAGE", "multiplier": 1.2, "can_crit": true}]}]}}, "PASSIVE_CRIT_BOOST": {"name": "暴擊強化", "description_template": "永久增加 {value}% 暴擊率", "skill_rarity": 2, "max_level": 10, "xp_to_next_level_config": {"base_xp": 120, "multiplier": 1.4}, "tags": ["CRIT", "PASSIVE_STAT"], "effects_by_level": {"1": [{"trigger_condition": {"type": "ON_BATTLE_START", "chance_formula": "1.0", "trigger_once_per_battle": true}, "target_override": {"selector_type": "SELF", "max_targets": 1}, "effect_definitions": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "crit_rate", "modification_type": "FLAT_ADD", "value_formula": "0.05"}]}]}], "5": [{"trigger_condition": {"type": "ON_BATTLE_START", "chance_formula": "1.0", "trigger_once_per_battle": true}, "target_override": {"selector_type": "SELF", "max_targets": 1}, "effect_definitions": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "crit_rate", "modification_type": "FLAT_ADD", "value_formula": "0.08"}]}]}], "10": [{"trigger_condition": {"type": "ON_BATTLE_START", "chance_formula": "1.0", "trigger_once_per_battle": true}, "target_override": {"selector_type": "SELF", "max_targets": 1}, "effect_definitions": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "crit_rate", "modification_type": "FLAT_ADD", "value_formula": "0.12"}]}]}]}}}