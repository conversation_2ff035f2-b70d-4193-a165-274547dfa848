"""
PlayerGlobalSkillProficiencyService - 玩家全局技能熟練度服務

負責玩家全局技能的學習、升級、獲取進度等操作。
"""

from typing import Dict, List, Optional, Tuple, Any, Literal, Union, TYPE_CHECKING
import logging

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader
    # from rpg_system.repositories.global_skill_repository import GlobalSkillRepository  # TODO: 實現後添加

logger = logging.getLogger(__name__)


class PlayerGlobalSkillProficiencyService:
    """
    玩家全局技能熟練度服務
    
    負責：
    1. 全局技能的學習和升級
    2. 技能經驗的管理
    3. 技能熟練度信息的查詢
    """
    
    def __init__(
        self,
        config_loader: 'ConfigLoader',
        # global_skill_repo: 'GlobalSkillRepository',  # TODO: 實現後添加
    ):
        self.config_loader = config_loader
        # self.global_skill_repo = global_skill_repo  # TODO: 實現後添加
        
        logger.info("PlayerGlobalSkillProficiencyService initialized")
    
    def learn_global_skill(
        self, 
        user_id: int, 
        skill_id: str, 
        skill_type: Literal["ACTIVE", "PASSIVE"]
    ) -> Tuple[bool, str]:
        """
        學習全局技能
        
        Args:
            user_id: 玩家ID
            skill_id: 技能ID
            skill_type: 技能類型
            
        Returns:
            (成功/失敗, 消息)
        """
        try:
            # 1. 檢查技能是否存在於配置中
            if skill_type == "ACTIVE":
                skill_config = self.config_loader.get_active_skill_config(skill_id)
            elif skill_type == "PASSIVE":
                skill_config = self.config_loader.get_passive_skill_config(skill_id)
            else:
                return False, f"無效的技能類型: {skill_type}"
            
            if not skill_config:
                return False, f"找不到技能: {skill_id}"
            
            # 2. TODO: 檢查玩家是否已學習此技能
            # if self.global_skill_repo.has_learned_skill(user_id, skill_id, skill_type):
            #     return False, f"已經學習過技能: {skill_config.name}"
            
            # 3. TODO: 添加學習記錄
            # self.global_skill_repo.add_learned_skill(
            #     user_id=user_id,
            #     skill_id=skill_id,
            #     skill_type=skill_type,
            #     initial_level=1,
            #     initial_xp=0
            # )
            
            logger.info(f"玩家 {user_id} 學習了 {skill_type} 技能: {skill_id}")
            return True, f"成功學習技能: {skill_config.name}"
            
        except Exception as e:
            logger.error(f"學習全局技能時發生錯誤: {e}", exc_info=True)
            return False, f"學習技能失敗: {str(e)}"
    
    def upgrade_global_skill(
        self, 
        user_id: int, 
        skill_id: str, 
        skill_type: Literal["ACTIVE", "PASSIVE"]
    ) -> Tuple[bool, str]:
        """
        升級全局技能
        
        Args:
            user_id: 玩家ID
            skill_id: 技能ID
            skill_type: 技能類型
            
        Returns:
            (成功/失敗, 消息)
        """
        try:
            # 1. TODO: 獲取玩家已學習的技能數據
            # learned_skill = self.global_skill_repo.get_learned_skill(user_id, skill_id, skill_type)
            # if not learned_skill:
            #     return False, f"尚未學習技能: {skill_id}"
            
            # 2. 獲取技能配置
            if skill_type == "ACTIVE":
                skill_config = self.config_loader.get_active_skill_config(skill_id)
            else:
                skill_config = self.config_loader.get_passive_skill_config(skill_id)
            
            if not skill_config:
                return False, f"找不到技能配置: {skill_id}"
            
            # 3. TODO: 檢查是否達到最大等級
            # if learned_skill.skill_level >= skill_config.max_level:
            #     return False, f"技能已達到最大等級: {skill_config.max_level}"
            
            # 4. TODO: 檢查是否有足夠的經驗
            # required_xp = self.get_skill_xp_requirements(skill_config, learned_skill.skill_level)
            # if required_xp is None:
            #     return False, "無法計算升級所需經驗"
            # 
            # if learned_skill.skill_xp < required_xp:
            #     return False, f"經驗不足: 需要 {required_xp}，當前 {learned_skill.skill_xp}"
            
            # 5. TODO: 執行升級
            # new_level = learned_skill.skill_level + 1
            # remaining_xp = learned_skill.skill_xp - required_xp
            # 
            # self.global_skill_repo.update_learned_skill(
            #     user_id=user_id,
            #     skill_id=skill_id,
            #     skill_type=skill_type,
            #     new_level=new_level,
            #     new_xp=remaining_xp
            # )
            
            # logger.info(f"玩家 {user_id} 的技能 {skill_id} 升級到 {new_level} 級")
            # return True, f"技能 {skill_config.name} 升級到 {new_level} 級"
            
            # 暫時返回成功（等待Repository實現）
            return True, f"技能升級功能待實現"
            
        except Exception as e:
            logger.error(f"升級全局技能時發生錯誤: {e}", exc_info=True)
            return False, f"升級技能失敗: {str(e)}"
    
    def add_xp_to_global_skill(
        self, 
        user_id: int, 
        skill_id: str, 
        skill_type: Literal["ACTIVE", "PASSIVE"], 
        xp_amount: int
    ) -> Tuple[Optional[Dict[str, Any]], str]:
        """
        為全局技能增加經驗
        
        Args:
            user_id: 玩家ID
            skill_id: 技能ID
            skill_type: 技能類型
            xp_amount: 經驗值
            
        Returns:
            (更新後的技能數據, 消息)
        """
        try:
            if xp_amount <= 0:
                return None, "經驗值必須大於0"
            
            # 1. TODO: 獲取玩家已學習的技能數據
            # learned_skill = self.global_skill_repo.get_learned_skill(user_id, skill_id, skill_type)
            # if not learned_skill:
            #     return None, f"尚未學習技能: {skill_id}"
            
            # 2. TODO: 增加經驗
            # new_xp = learned_skill.skill_xp + xp_amount
            # self.global_skill_repo.update_skill_xp(user_id, skill_id, skill_type, new_xp)
            
            # 3. TODO: 檢查是否可以升級
            # skill_config = self.config_loader.get_active_skill_config(skill_id) if skill_type == "ACTIVE" else self.config_loader.get_passive_skill_config(skill_id)
            # if skill_config:
            #     required_xp = self.get_skill_xp_requirements(skill_config, learned_skill.skill_level)
            #     if required_xp and new_xp >= required_xp:
            #         # 可以升級，但不自動升級，讓玩家手動升級
            #         pass
            
            # updated_skill = self.global_skill_repo.get_learned_skill(user_id, skill_id, skill_type)
            # logger.info(f"玩家 {user_id} 的技能 {skill_id} 增加了 {xp_amount} 經驗")
            # return updated_skill.to_dict(), f"成功增加 {xp_amount} 經驗"
            
            # 暫時返回成功（等待Repository實現）
            return {"skill_id": skill_id, "xp_added": xp_amount}, f"成功增加 {xp_amount} 經驗"
            
        except Exception as e:
            logger.error(f"增加技能經驗時發生錯誤: {e}", exc_info=True)
            return None, f"增加經驗失敗: {str(e)}"
    
    def get_player_learned_skills(
        self, 
        user_id: int, 
        skill_type: Optional[Literal["ACTIVE", "PASSIVE"]] = None
    ) -> List[Dict[str, Any]]:
        """
        獲取玩家已學習的技能列表
        
        Args:
            user_id: 玩家ID
            skill_type: 技能類型過濾（可選）
            
        Returns:
            技能信息列表
        """
        try:
            # TODO: 從Repository獲取技能列表
            # learned_skills = self.global_skill_repo.get_player_learned_skills(user_id, skill_type)
            
            # formatted_skills = []
            # for learned_skill in learned_skills:
            #     # 獲取技能配置
            #     if learned_skill.skill_type == "ACTIVE":
            #         skill_config = self.config_loader.get_active_skill_config(learned_skill.skill_id)
            #     else:
            #         skill_config = self.config_loader.get_passive_skill_config(learned_skill.skill_id)
            #     
            #     if skill_config:
            #         # 計算升級所需經驗
            #         required_xp = self.get_skill_xp_requirements(skill_config, learned_skill.skill_level)
            #         
            #         formatted_skills.append({
            #             "skill_id": learned_skill.skill_id,
            #             "skill_type": learned_skill.skill_type,
            #             "name": skill_config.name,
            #             "description": skill_config.description_template,
            #             "current_level": learned_skill.skill_level,
            #             "max_level": skill_config.max_level,
            #             "current_xp": learned_skill.skill_xp,
            #             "xp_to_next_level": required_xp,
            #             "can_upgrade": required_xp and learned_skill.skill_xp >= required_xp,
            #             "unlocked_at": learned_skill.unlocked_at
            #         })
            # 
            # return formatted_skills
            
            # 暫時返回空列表（等待Repository實現）
            return []
            
        except Exception as e:
            logger.error(f"獲取玩家技能列表時發生錯誤: {e}", exc_info=True)
            return []
    
    def get_skill_xp_requirements(
        self, 
        skill_config: Union[Any, Any], 
        current_level: int
    ) -> Optional[int]:
        """
        計算技能升級所需經驗
        
        Args:
            skill_config: 技能配置
            current_level: 當前等級
            
        Returns:
            升級所需經驗，如果已達到最大等級則返回None
        """
        try:
            if current_level >= skill_config.max_level:
                return None
            
            # TODO: 根據技能配置中的 xp_to_next_level_config 計算
            # xp_config = getattr(skill_config, 'xp_to_next_level_config', None)
            # if not xp_config:
            #     # 使用默認公式：基礎經驗 * 等級
            #     base_xp = 100
            #     return base_xp * current_level
            # 
            # # 解析配置中的經驗公式
            # if isinstance(xp_config, dict):
            #     formula = xp_config.get('formula', 'base_xp * level')
            #     base_xp = xp_config.get('base_xp', 100)
            #     
            #     # 使用公式引擎計算
            #     context = {
            #         'level': current_level,
            #         'base_xp': base_xp,
            #         'max_level': skill_config.max_level
            #     }
            #     
            #     # TODO: 使用FormulaEvaluator計算
            #     # return self.formula_evaluator.evaluate(formula, context)
            
            # 暫時使用簡單公式
            return 100 * current_level
            
        except Exception as e:
            logger.error(f"計算技能經驗需求時發生錯誤: {e}", exc_info=True)
            return None
