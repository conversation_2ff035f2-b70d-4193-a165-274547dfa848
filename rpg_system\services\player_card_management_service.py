"""
PlayerCardManagementService - 玩家卡牌管理服務

負責玩家卡牌的技能槽位管理、被動技能裝備/卸下等操作。
"""

from typing import Dict, List, Optional, Tuple, Any, Literal, TYPE_CHECKING
import logging

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader
    from rpg_system.repositories.player_collection_repository import PlayerCollectionRpgRepository

logger = logging.getLogger(__name__)


class PlayerCardManagementService:
    """
    玩家卡牌管理服務
    
    負責：
    1. 主動技能的裝備和卸下
    2. 被動技能的裝備和卸下
    3. 卡牌詳細信息的獲取和展示
    """
    
    def __init__(
        self,
        config_loader: 'ConfigLoader',
        player_collection_repo: 'PlayerCollectionRpgRepository',
        # global_skill_repo: 'GlobalSkillRepository',  # TODO: 實現後添加
    ):
        self.config_loader = config_loader
        self.player_collection_repo = player_collection_repo
        # self.global_skill_repo = global_skill_repo  # TODO: 實現後添加
        
        logger.info("PlayerCardManagementService initialized")
    
    def equip_active_skill(
        self, 
        user_id: int, 
        collection_id: int, 
        active_skill_id: str, 
        slot_index: int
    ) -> Tuple[bool, str]:
        """
        裝備主動技能
        
        Args:
            user_id: 玩家ID
            collection_id: 卡牌收藏ID
            active_skill_id: 主動技能ID
            slot_index: 技能槽位索引
            
        Returns:
            (成功/失敗, 消息)
        """
        try:
            # 1. 獲取玩家卡牌數據
            player_card = self.player_collection_repo.get_player_card_by_collection_id(collection_id)
            if not player_card:
                return False, f"找不到卡牌: {collection_id}"
            
            if player_card.user_id != user_id:
                return False, "無權限操作此卡牌"
            
            # 2. 獲取卡牌配置
            card_config = self.config_loader.get_card_config(player_card.card_id)
            if not card_config:
                return False, f"找不到卡牌配置: {player_card.card_id}"
            
            # 3. 驗證槽位索引
            max_active_slots = getattr(card_config, 'max_active_skill_slots', 3)  # 默認3個槽位
            if slot_index < 0 or slot_index >= max_active_slots:
                return False, f"無效的槽位索引: {slot_index}"
            
            # 4. 驗證技能存在
            skill_config = self.config_loader.get_active_skill_config(active_skill_id)
            if not skill_config:
                return False, f"找不到主動技能: {active_skill_id}"
            
            # 5. TODO: 驗證玩家是否已學習此技能
            # if not self.global_skill_repo.has_learned_skill(user_id, active_skill_id, "ACTIVE"):
            #     return False, f"尚未學習技能: {active_skill_id}"
            
            # 6. 更新技能槽位
            equipped_skills = player_card.equipped_active_skill_ids or []
            
            # 確保列表長度足夠
            while len(equipped_skills) <= slot_index:
                equipped_skills.append(None)
            
            # 設置技能
            equipped_skills[slot_index] = active_skill_id
            
            # 7. 保存更新
            player_card.equipped_active_skill_ids = equipped_skills
            self.player_collection_repo.update_player_card_skills(player_card)
            
            logger.info(f"玩家 {user_id} 為卡牌 {collection_id} 裝備主動技能 {active_skill_id} 到槽位 {slot_index}")
            return True, f"成功裝備技能 {skill_config.name}"
            
        except Exception as e:
            logger.error(f"裝備主動技能時發生錯誤: {e}", exc_info=True)
            return False, f"裝備技能失敗: {str(e)}"
    
    def unequip_active_skill(
        self, 
        user_id: int, 
        collection_id: int, 
        slot_index: int
    ) -> Tuple[bool, str]:
        """
        卸下主動技能
        
        Args:
            user_id: 玩家ID
            collection_id: 卡牌收藏ID
            slot_index: 技能槽位索引
            
        Returns:
            (成功/失敗, 消息)
        """
        try:
            # 1. 獲取玩家卡牌數據
            player_card = self.player_collection_repo.get_player_card_by_collection_id(collection_id)
            if not player_card:
                return False, f"找不到卡牌: {collection_id}"
            
            if player_card.user_id != user_id:
                return False, "無權限操作此卡牌"
            
            # 2. 驗證槽位索引
            equipped_skills = player_card.equipped_active_skill_ids or []
            if slot_index < 0 or slot_index >= len(equipped_skills):
                return False, f"無效的槽位索引: {slot_index}"
            
            # 3. 檢查槽位是否有技能
            if not equipped_skills[slot_index]:
                return False, "該槽位沒有裝備技能"
            
            # 4. 卸下技能
            old_skill_id = equipped_skills[slot_index]
            equipped_skills[slot_index] = None
            
            # 5. 保存更新
            player_card.equipped_active_skill_ids = equipped_skills
            self.player_collection_repo.update_player_card_skills(player_card)
            
            logger.info(f"玩家 {user_id} 從卡牌 {collection_id} 的槽位 {slot_index} 卸下技能 {old_skill_id}")
            return True, f"成功卸下技能"
            
        except Exception as e:
            logger.error(f"卸下主動技能時發生錯誤: {e}", exc_info=True)
            return False, f"卸下技能失敗: {str(e)}"
    
    def equip_passive_skill(
        self, 
        user_id: int, 
        collection_id: int, 
        passive_skill_id: str, 
        passive_skill_level: int, 
        slot_key: str
    ) -> Tuple[bool, str]:
        """
        裝備被動技能
        
        Args:
            user_id: 玩家ID
            collection_id: 卡牌收藏ID
            passive_skill_id: 被動技能ID
            passive_skill_level: 被動技能等級
            slot_key: 槽位鍵（如 "slot_0", "slot_1"）
            
        Returns:
            (成功/失敗, 消息)
        """
        try:
            # 1. 獲取玩家卡牌數據
            player_card = self.player_collection_repo.get_player_card_by_collection_id(collection_id)
            if not player_card:
                return False, f"找不到卡牌: {collection_id}"
            
            if player_card.user_id != user_id:
                return False, "無權限操作此卡牌"
            
            # 2. 獲取卡牌配置
            card_config = self.config_loader.get_card_config(player_card.card_id)
            if not card_config:
                return False, f"找不到卡牌配置: {player_card.card_id}"
            
            # 3. 驗證槽位鍵格式
            if not slot_key.startswith("slot_"):
                return False, f"無效的槽位鍵格式: {slot_key}"
            
            try:
                slot_index = int(slot_key.split("_")[1])
            except (IndexError, ValueError):
                return False, f"無效的槽位鍵: {slot_key}"
            
            # 4. 驗證槽位數量
            max_passive_slots = getattr(card_config, 'passive_skill_slots', 0)
            if slot_index >= max_passive_slots:
                return False, f"槽位 {slot_index} 超出可用槽位數量 {max_passive_slots}"
            
            # 5. 驗證技能存在
            skill_config = self.config_loader.get_passive_skill_config(passive_skill_id)
            if not skill_config:
                return False, f"找不到被動技能: {passive_skill_id}"
            
            # 6. TODO: 驗證玩家是否已學習此技能且等級足夠
            # if not self.global_skill_repo.has_learned_skill(user_id, passive_skill_id, "PASSIVE"):
            #     return False, f"尚未學習技能: {passive_skill_id}"
            # 
            # player_skill_level = self.global_skill_repo.get_skill_level(user_id, passive_skill_id, "PASSIVE")
            # if player_skill_level < passive_skill_level:
            #     return False, f"技能等級不足: 需要 {passive_skill_level}，當前 {player_skill_level}"
            
            # 7. 更新被動技能槽位
            equipped_passives = player_card.equipped_common_passives or {}
            equipped_passives[slot_key] = {
                "skill_id": passive_skill_id,
                "level": passive_skill_level
            }
            
            # 8. 保存更新
            player_card.equipped_common_passives = equipped_passives
            self.player_collection_repo.update_player_card_passives(player_card)
            
            logger.info(f"玩家 {user_id} 為卡牌 {collection_id} 裝備被動技能 {passive_skill_id} (等級 {passive_skill_level}) 到 {slot_key}")
            return True, f"成功裝備被動技能 {skill_config.name}"
            
        except Exception as e:
            logger.error(f"裝備被動技能時發生錯誤: {e}", exc_info=True)
            return False, f"裝備被動技能失敗: {str(e)}"

    def unequip_passive_skill(
        self,
        user_id: int,
        collection_id: int,
        slot_key: str
    ) -> Tuple[bool, str]:
        """
        卸下被動技能

        Args:
            user_id: 玩家ID
            collection_id: 卡牌收藏ID
            slot_key: 槽位鍵（如 "slot_0", "slot_1"）

        Returns:
            (成功/失敗, 消息)
        """
        try:
            # 1. 獲取玩家卡牌數據
            player_card = self.player_collection_repo.get_player_card_by_collection_id(collection_id)
            if not player_card:
                return False, f"找不到卡牌: {collection_id}"

            if player_card.user_id != user_id:
                return False, "無權限操作此卡牌"

            # 2. 檢查槽位是否有技能
            equipped_passives = player_card.equipped_common_passives or {}
            if slot_key not in equipped_passives:
                return False, f"槽位 {slot_key} 沒有裝備技能"

            # 3. 卸下技能
            old_skill_data = equipped_passives.pop(slot_key)
            old_skill_id = old_skill_data.get('skill_id', 'unknown') if isinstance(old_skill_data, dict) else str(old_skill_data)

            # 4. 保存更新
            player_card.equipped_common_passives = equipped_passives
            self.player_collection_repo.update_player_card_passives(player_card)

            logger.info(f"玩家 {user_id} 從卡牌 {collection_id} 的 {slot_key} 卸下被動技能 {old_skill_id}")
            return True, "成功卸下被動技能"

        except Exception as e:
            logger.error(f"卸下被動技能時發生錯誤: {e}", exc_info=True)
            return False, f"卸下被動技能失敗: {str(e)}"

    def get_card_details_for_display(
        self,
        user_id: int,
        collection_id: int
    ) -> Optional[Dict[str, Any]]:
        """
        獲取卡牌詳細信息用於展示

        Args:
            user_id: 玩家ID
            collection_id: 卡牌收藏ID

        Returns:
            卡牌詳細信息字典，如果失敗則返回None
        """
        try:
            # 1. 獲取玩家卡牌數據
            player_card = self.player_collection_repo.get_player_card_by_collection_id(collection_id)
            if not player_card:
                logger.warning(f"找不到卡牌: {collection_id}")
                return None

            if player_card.user_id != user_id:
                logger.warning(f"玩家 {user_id} 無權限查看卡牌 {collection_id}")
                return None

            # 2. 獲取卡牌配置
            card_config = self.config_loader.get_card_config(player_card.card_id)
            if not card_config:
                logger.warning(f"找不到卡牌配置: {player_card.card_id}")
                return None

            # 3. TODO: 計算卡牌屬性
            # 這裡需要使用 AttributeCalculator 來計算最終屬性
            # calculated_stats = self.attribute_calculator.calculate_attributes(...)

            # 4. 構建詳細信息
            card_details = {
                "collection_id": collection_id,
                "card_id": player_card.card_id,
                "name": card_config.name,
                "rarity": getattr(card_config, 'rarity', 'N'),
                "rpg_level": player_card.rpg_level,
                "rpg_xp": player_card.rpg_xp,
                "star_level": player_card.star_level,
                "is_favorite": player_card.is_favorite,

                # 基礎屬性（從配置）
                "base_stats": getattr(card_config, 'base_stats', {}),
                "growth_per_rpg_level": getattr(card_config, 'growth_per_rpg_level', {}),

                # TODO: 計算後的屬性
                # "calculated_stats": calculated_stats,

                # 技能信息
                "primary_attack_skill_id": getattr(card_config, 'primary_attack_skill_id', None),
                "innate_passive_skill_id": getattr(card_config, 'innate_passive_skill_id', None),
                "equipped_active_skills": self._format_active_skills(player_card.equipped_active_skill_ids),
                "equipped_passive_skills": self._format_passive_skills(player_card.equipped_common_passives),

                # 槽位信息
                "max_active_skill_slots": getattr(card_config, 'max_active_skill_slots', 3),
                "passive_skill_slots": getattr(card_config, 'passive_skill_slots', 0),

                # 時間信息
                "first_acquired": player_card.first_acquired,
                "last_acquired": player_card.last_acquired,
            }

            return card_details

        except Exception as e:
            logger.error(f"獲取卡牌詳細信息時發生錯誤: {e}", exc_info=True)
            return None

    def _format_active_skills(self, equipped_active_skill_ids: Optional[List[str]]) -> List[Dict[str, Any]]:
        """
        格式化主動技能信息

        Args:
            equipped_active_skill_ids: 裝備的主動技能ID列表

        Returns:
            格式化的技能信息列表
        """
        if not equipped_active_skill_ids:
            return []

        formatted_skills = []
        for i, skill_id in enumerate(equipped_active_skill_ids):
            if skill_id:
                skill_config = self.config_loader.get_active_skill_config(skill_id)
                formatted_skills.append({
                    "slot_index": i,
                    "skill_id": skill_id,
                    "name": skill_config.name if skill_config else "未知技能",
                    "description": skill_config.description_template if skill_config else "",
                    # TODO: 添加技能等級信息
                    # "level": self.global_skill_repo.get_skill_level(user_id, skill_id, "ACTIVE")
                })
            else:
                formatted_skills.append({
                    "slot_index": i,
                    "skill_id": None,
                    "name": "空槽位",
                    "description": "",
                })

        return formatted_skills

    def _format_passive_skills(self, equipped_common_passives: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        格式化被動技能信息

        Args:
            equipped_common_passives: 裝備的被動技能字典

        Returns:
            格式化的技能信息列表
        """
        if not equipped_common_passives:
            return []

        formatted_skills = []
        for slot_key, passive_data in equipped_common_passives.items():
            if passive_data and isinstance(passive_data, dict):
                skill_id = passive_data.get('skill_id')
                level = passive_data.get('level', 1)

                if skill_id:
                    skill_config = self.config_loader.get_passive_skill_config(skill_id)
                    formatted_skills.append({
                        "slot_key": slot_key,
                        "skill_id": skill_id,
                        "name": skill_config.name if skill_config else "未知技能",
                        "description": skill_config.description_template if skill_config else "",
                        "level": level
                    })

        return formatted_skills
